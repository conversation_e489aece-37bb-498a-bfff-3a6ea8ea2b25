﻿#pragma checksum "..\..\..\..\Pages\PaymentsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DECAF196A0380E4092769E599A2F097D3FC03357"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Converters;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// PaymentsPage
    /// </summary>
    public partial class PaymentsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 268 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayPaymentsAmountText;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayPaymentsCountText;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthlyPaymentsAmountText;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthlyPaymentsCountText;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AveragePaymentText;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CashPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 375 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CardPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 385 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickFilterTodayButton;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickFilterThisMonthButton;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickFilterCashButton;
        
        #line default
        #line hidden
        
        
        #line 415 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickFilterCardButton;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickFilterAllButton;
        
        #line default
        #line hidden
        
        
        #line 438 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 448 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMultiPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 458 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 486 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 501 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 510 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 694 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 711 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyStatePanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/paymentspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\PaymentsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 2:
            this.TotalPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TodayPaymentsAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TodayPaymentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.MonthlyPaymentsAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.MonthlyPaymentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AveragePaymentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.CashPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CardPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.QuickFilterTodayButton = ((System.Windows.Controls.Button)(target));
            
            #line 385 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.QuickFilterTodayButton.Click += new System.Windows.RoutedEventHandler(this.QuickFilterToday_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.QuickFilterThisMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 395 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.QuickFilterThisMonthButton.Click += new System.Windows.RoutedEventHandler(this.QuickFilterThisMonth_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.QuickFilterCashButton = ((System.Windows.Controls.Button)(target));
            
            #line 405 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.QuickFilterCashButton.Click += new System.Windows.RoutedEventHandler(this.QuickFilterCash_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.QuickFilterCardButton = ((System.Windows.Controls.Button)(target));
            
            #line 415 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.QuickFilterCardButton.Click += new System.Windows.RoutedEventHandler(this.QuickFilterCard_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.QuickFilterAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 425 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.QuickFilterAllButton.Click += new System.Windows.RoutedEventHandler(this.QuickFilterAll_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 441 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.AddMultiPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 451 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddMultiPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddMultiPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 461 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 492 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.PaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 520 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PaymentsDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.PaymentsDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 22:
            this.LoadingPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 23:
            this.EmptyStatePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            System.Windows.EventSetter eventSetter;
            switch (connectionId)
            {
            case 1:
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.MouseEnterEvent;
            
            #line 25 "..\..\..\..\Pages\PaymentsPage.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.StatCard_MouseEnter);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.MouseLeaveEvent;
            
            #line 26 "..\..\..\..\Pages\PaymentsPage.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.StatCard_MouseLeave);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            break;
            case 21:
            
            #line 634 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewPaymentAttachment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

