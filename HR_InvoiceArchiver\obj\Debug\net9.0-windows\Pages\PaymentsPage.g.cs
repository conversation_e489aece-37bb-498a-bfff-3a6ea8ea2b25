﻿#pragma checksum "..\..\..\..\Pages\PaymentsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "74AEF35AFF802387BB60FFECBEB02430CF57FFDC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// PaymentsPage
    /// </summary>
    public partial class PaymentsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 221 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentTimeText;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentDateText;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CashPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CardPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AveragePaymentText;
        
        #line default
        #line hidden
        
        
        #line 421 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 432 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMultiPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 443 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 455 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 513 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 520 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodFilter;
        
        #line default
        #line hidden
        
        
        #line 531 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateFromFilter;
        
        #line default
        #line hidden
        
        
        #line 538 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateToFilter;
        
        #line default
        #line hidden
        
        
        #line 555 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountFromFilter;
        
        #line default
        #line hidden
        
        
        #line 562 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountToFilter;
        
        #line default
        #line hidden
        
        
        #line 569 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SupplierFilter;
        
        #line default
        #line hidden
        
        
        #line 579 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 587 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 623 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 629 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 635 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 803 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DisplayedAmountText;
        
        #line default
        #line hidden
        
        
        #line 809 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveFiltersText;
        
        #line default
        #line hidden
        
        
        #line 815 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DataStatusText;
        
        #line default
        #line hidden
        
        
        #line 823 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 843 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid EmptyStatePanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/paymentspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\PaymentsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CurrentTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CurrentDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TotalPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CashPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.CardPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AveragePaymentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 424 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AddMultiPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 435 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddMultiPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddMultiPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 447 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 458 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 470 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 518 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.PaymentMethodFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 525 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PaymentMethodFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentMethodFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.DateFromFilter = ((System.Windows.Controls.DatePicker)(target));
            
            #line 536 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.DateFromFilter.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFromFilter_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.DateToFilter = ((System.Windows.Controls.DatePicker)(target));
            
            #line 543 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.DateToFilter.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateToFilter_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.AmountFromFilter = ((System.Windows.Controls.TextBox)(target));
            
            #line 560 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AmountFromFilter.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountFromFilter_TextChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.AmountToFilter = ((System.Windows.Controls.TextBox)(target));
            
            #line 567 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AmountToFilter.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountToFilter_TextChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.SupplierFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 574 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.SupplierFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SupplierFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ClearFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 585 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ClearFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ClearFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ApplyFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 595 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ApplyFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ApplyFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.PaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 25:
            this.DisplayedAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.ActiveFiltersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.DataStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.LoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 29:
            this.EmptyStatePanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 30:
            
            #line 861 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 870 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

