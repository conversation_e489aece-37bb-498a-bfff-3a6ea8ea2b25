using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class PaymentsPage : UserControl, INavigationAware
    {
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;
        private readonly ISupplierService _supplierService;

        private ObservableCollection<Payment> _allPayments;
        private ObservableCollection<Payment> _filteredPayments;
        private ObservableCollection<Supplier> _suppliers;
        private DispatcherTimer _clockTimer;
        private int _activeFiltersCount = 0;

        public PaymentsPage(
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: Constructor started");
                InitializeComponent();

                _paymentService = paymentService;
                _toastService = toastService;
                _navigationService = navigationService;

                // Try to get supplier service from DI if available
                try
                {
                    _supplierService = App.ServiceProvider?.GetService(typeof(ISupplierService)) as ISupplierService;
                }
                catch
                {
                    _supplierService = null;
                }

                _allPayments = new ObservableCollection<Payment>();
                _filteredPayments = new ObservableCollection<Payment>();
                _suppliers = new ObservableCollection<Supplier>();

                PaymentsDataGrid.ItemsSource = _filteredPayments;

                InitializeUI();
                StartClockTimer();

                System.Console.WriteLine("PaymentsPage: Constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Constructor error: {ex.Message}");
                throw;
            }
        }

        private void InitializeUI()
        {
            // Set current date and time
            CurrentDateText.Text = DateTime.Now.ToString("dd/MM/yyyy");
            CurrentTimeText.Text = DateTime.Now.ToString("HH:mm:ss");

            // Initialize filter counts
            ActiveFiltersText.Text = "0";
            DataStatusText.Text = "جاهز للتحميل";
        }

        private void StartClockTimer()
        {
            _clockTimer = new DispatcherTimer();
            _clockTimer.Interval = TimeSpan.FromSeconds(1);
            _clockTimer.Tick += (s, e) => CurrentTimeText.Text = DateTime.Now.ToString("HH:mm:ss");
            _clockTimer.Start();
        }

        public async void OnNavigatedTo(object parameter)
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedTo called");
            try
            {
                // Handle navigation parameters
                if (parameter is string action && action == "add")
                {
                    System.Console.WriteLine("PaymentsPage: Add payment parameter detected");
                    AddPaymentButton_Click(this, new RoutedEventArgs());
                }
                else if (parameter is string multiAction && multiAction == "multi")
                {
                    System.Console.WriteLine("PaymentsPage: Multi payment parameter detected");
                    AddMultiPaymentButton_Click(this, new RoutedEventArgs());
                }
                else
                {
                    System.Console.WriteLine("PaymentsPage: Loading payments data");
                    await LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: OnNavigatedTo error: {ex.Message}");
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء التنقل: {ex.Message}");
            }
        }

        public void OnNavigatedFrom()
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedFrom called");
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync started");

                // Show loading
                LoadingPanel.Visibility = Visibility.Visible;
                EmptyStatePanel.Visibility = Visibility.Collapsed;
                DataStatusText.Text = "جاري التحميل...";

                // Load payments and suppliers
                var payments = await _paymentService.GetAllPaymentsAsync();

                List<Supplier> suppliers = new List<Supplier>();
                if (_supplierService != null)
                {
                    suppliers = await _supplierService.GetAllSuppliersAsync();
                }

                _allPayments.Clear();
                _filteredPayments.Clear();
                _suppliers.Clear();

                foreach (var payment in payments)
                {
                    _allPayments.Add(payment);
                    _filteredPayments.Add(payment);
                }

                foreach (var supplier in suppliers)
                {
                    _suppliers.Add(supplier);
                }

                // Update supplier filter
                UpdateSupplierFilter();

                // Update statistics
                await UpdateStatisticsAsync();

                // Hide loading and show content
                LoadingPanel.Visibility = Visibility.Collapsed;
                DataStatusText.Text = "محدثة";

                UpdateUIVisibility();

                // Update last update time
                LastUpdateText.Text = DateTime.Now.ToString("HH:mm:ss");

                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync completed successfully");
                _toastService?.ShowSuccess("تم التحديث", "تم تحميل المدفوعات بنجاح");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: LoadPaymentsAsync error: {ex.Message}");
                LoadingPanel.Visibility = Visibility.Collapsed;
                DataStatusText.Text = "خطأ في التحميل";
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء تحميل المدفوعات: {ex.Message}");
            }
        }

        private void UpdateSupplierFilter()
        {
            SupplierFilter.Items.Clear();
            SupplierFilter.Items.Add(new ComboBoxItem { Content = "جميع الموردين" });

            foreach (var supplier in _suppliers.OrderBy(s => s.Name))
            {
                SupplierFilter.Items.Add(new ComboBoxItem { Content = supplier.Name, Tag = supplier.Id });
            }

            SupplierFilter.SelectedIndex = 0;
        }

        private void UpdateUIVisibility()
        {
            if (_filteredPayments.Count == 0)
            {
                EmptyStatePanel.Visibility = Visibility.Visible;
            }
            else
            {
                EmptyStatePanel.Visibility = Visibility.Collapsed;
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var statistics = await _paymentService.GetPaymentStatisticsAsync();

                // Update statistics cards
                TotalPaymentsText.Text = statistics.TotalPayments.ToString("N0");
                TotalAmountText.Text = $"{statistics.TotalAmount:N0}";

                // Update cash vs card statistics
                var cashCount = _allPayments.Count(p => p.Method == PaymentMethod.Cash);
                var cardCount = _allPayments.Count(p => p.Method == PaymentMethod.CreditCard);

                CashPaymentsText.Text = cashCount.ToString("N0");
                CardPaymentsText.Text = cardCount.ToString("N0");

                // Calculate average payment
                var averagePayment = _allPayments.Count > 0 ? _allPayments.Average(p => p.Amount) : 0;
                AveragePaymentText.Text = $"{averagePayment:N0}";

                // Update payment count and displayed amount
                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة";
                var displayedAmount = _filteredPayments.Sum(p => p.Amount);
                DisplayedAmountText.Text = $"{displayedAmount:N0} د.ع";

                System.Console.WriteLine("PaymentsPage: Statistics updated successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: UpdateStatisticsAsync error: {ex.Message}");
            }
        }

        private void FilterPayments()
        {
            try
            {
                var searchText = SearchTextBox.Text?.ToLower() ?? "";
                var selectedMethod = PaymentMethodFilter.SelectedItem as ComboBoxItem;
                var selectedSupplier = SupplierFilter.SelectedItem as ComboBoxItem;
                var dateFrom = DateFromFilter.SelectedDate;
                var dateTo = DateToFilter.SelectedDate;

                decimal.TryParse(AmountFromFilter.Text, out decimal amountFrom);
                decimal.TryParse(AmountToFilter.Text, out decimal amountTo);

                _activeFiltersCount = 0;

                var filtered = _allPayments.Where(payment =>
                {
                    // Search filter
                    bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                                       payment.ReceiptNumber.ToLower().Contains(searchText) ||
                                       payment.SupplierName.ToLower().Contains(searchText) ||
                                       payment.Details.ToLower().Contains(searchText);

                    // Payment method filter
                    bool matchesMethod = selectedMethod == null ||
                                       selectedMethod.Content.ToString() == "الكل" ||
                                       (selectedMethod.Content.ToString() == "نقدي" && payment.Method == PaymentMethod.Cash) ||
                                       (selectedMethod.Content.ToString() == "بطاقة ائتمان" && payment.Method == PaymentMethod.CreditCard);

                    if (selectedMethod != null && selectedMethod.Content.ToString() != "الكل")
                        _activeFiltersCount++;

                    // Supplier filter
                    bool matchesSupplier = selectedSupplier == null ||
                                         selectedSupplier.Content.ToString() == "جميع الموردين" ||
                                         payment.SupplierName == selectedSupplier.Content.ToString();

                    if (selectedSupplier != null && selectedSupplier.Content.ToString() != "جميع الموردين")
                        _activeFiltersCount++;

                    // Date range filter
                    bool matchesDateRange = true;
                    if (dateFrom.HasValue)
                    {
                        matchesDateRange = payment.PaymentDate.Date >= dateFrom.Value.Date;
                        _activeFiltersCount++;
                    }
                    if (dateTo.HasValue)
                    {
                        matchesDateRange = matchesDateRange && payment.PaymentDate.Date <= dateTo.Value.Date;
                        if (!dateFrom.HasValue) _activeFiltersCount++;
                    }

                    // Amount range filter
                    bool matchesAmountRange = true;
                    if (amountFrom > 0)
                    {
                        matchesAmountRange = payment.Amount >= amountFrom;
                        _activeFiltersCount++;
                    }
                    if (amountTo > 0)
                    {
                        matchesAmountRange = matchesAmountRange && payment.Amount <= amountTo;
                        if (amountFrom <= 0) _activeFiltersCount++;
                    }

                    return matchesSearch && matchesMethod && matchesSupplier && matchesDateRange && matchesAmountRange;
                }).ToList();

                _filteredPayments.Clear();
                foreach (var payment in filtered)
                {
                    _filteredPayments.Add(payment);
                }

                // Update UI
                UpdateUIVisibility();

                // Update counts and statistics
                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة";
                var displayedAmount = _filteredPayments.Sum(p => p.Amount);
                DisplayedAmountText.Text = $"{displayedAmount:N0} د.ع";
                ActiveFiltersText.Text = _activeFiltersCount.ToString();

                System.Console.WriteLine($"PaymentsPage: Filtered {_filteredPayments.Count} payments with {_activeFiltersCount} active filters");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: FilterPayments error: {ex.Message}");
            }
        }

        // Event Handlers
        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("إضافة مدفوعة", "سيتم فتح نافذة إضافة مدفوعة قريباً");
        }

        private void AddMultiPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("وصل متعدد", "سيتم فتح نافذة الوصل المتعدد قريباً");
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPaymentsAsync();
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("تصدير البيانات", "سيتم إضافة ميزة التصدير قريباً");
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("طباعة", "سيتم إضافة ميزة الطباعة قريباً");
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterPayments();
        }

        private void PaymentMethodFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterPayments();
        }

        private void SupplierFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterPayments();
        }

        private void DateFromFilter_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterPayments();
        }

        private void DateToFilter_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterPayments();
        }

        private void AmountFromFilter_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterPayments();
        }

        private void AmountToFilter_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterPayments();
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            // Clear all filters
            SearchTextBox.Text = "";
            PaymentMethodFilter.SelectedIndex = 0;
            SupplierFilter.SelectedIndex = 0;
            DateFromFilter.SelectedDate = null;
            DateToFilter.SelectedDate = null;
            AmountFromFilter.Text = "";
            AmountToFilter.Text = "";

            FilterPayments();
            _toastService?.ShowSuccess("تم المسح", "تم مسح جميع الفلاتر");
        }

        private void ApplyFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            FilterPayments();
            _toastService?.ShowInfo("تم التطبيق", $"تم تطبيق {_activeFiltersCount} فلتر");
        }

        public void Dispose()
        {
            _clockTimer?.Stop();
        }
    }
}
