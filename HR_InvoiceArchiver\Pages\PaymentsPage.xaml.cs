using System;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class PaymentsPage : UserControl, INavigationAware
    {
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;

        public PaymentsPage(
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: Constructor started");
                InitializeComponent();
                System.Console.WriteLine("PaymentsPage: InitializeComponent completed");
                
                _paymentService = paymentService;
                _toastService = toastService;
                _navigationService = navigationService;
                
                System.Console.WriteLine("PaymentsPage: Services assigned");
                System.Console.WriteLine("PaymentsPage: Constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Constructor error: {ex.Message}");
                System.Console.WriteLine($"PaymentsPage: Constructor stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public void OnNavigatedTo(object parameter)
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedTo called");
            try
            {
                _toastService?.ShowSuccess("نجح", "تم تحميل واجهة المدفوعات بنجاح!");
                
                // Update basic statistics
                TotalPaymentsText.Text = "5";
                TotalAmountText.Text = "1,250,000 د.ع";
                CashPaymentsText.Text = "3";
                CardPaymentsText.Text = "2";
                PaymentCountText.Text = "5 دفعات";
                
                System.Console.WriteLine("PaymentsPage: OnNavigatedTo completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: OnNavigatedTo error: {ex.Message}");
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء التنقل: {ex.Message}");
            }
        }

        public void OnNavigatedFrom()
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedFrom called");
        }

        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("إضافة مدفوعة", "سيتم إضافة هذه الميزة قريباً");
        }

        private void AddMultiPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("وصل متعدد", "سيتم إضافة هذه الميزة قريباً");
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Search functionality will be implemented later
        }
    }
}
