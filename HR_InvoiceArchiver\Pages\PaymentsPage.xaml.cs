using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class PaymentsPage : UserControl, INavigationAware
    {
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;
        private ObservableCollection<Payment> _allPayments;
        private ObservableCollection<Payment> _filteredPayments;

        public PaymentsPage(
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: Constructor started");
                InitializeComponent();

                _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
                _toastService = toastService ?? throw new ArgumentNullException(nameof(toastService));
                _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));

                _allPayments = new ObservableCollection<Payment>();
                _filteredPayments = new ObservableCollection<Payment>();

                PaymentsDataGrid.ItemsSource = _filteredPayments;

                System.Console.WriteLine("PaymentsPage: Constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Constructor error: {ex.Message}");
                System.Console.WriteLine($"PaymentsPage: Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async void OnNavigatedTo(object parameter)
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedTo called");
            try
            {
                // Handle navigation parameters
                if (parameter is string action && action == "add")
                {
                    System.Console.WriteLine("PaymentsPage: Add payment parameter detected");
                    AddPaymentButton_Click(this, new RoutedEventArgs());
                }
                else if (parameter is string multiAction && multiAction == "multi")
                {
                    System.Console.WriteLine("PaymentsPage: Multi payment parameter detected");
                    AddMultiPaymentButton_Click(this, new RoutedEventArgs());
                }
                else
                {
                    System.Console.WriteLine("PaymentsPage: Loading payments data");
                    await LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: OnNavigatedTo error: {ex.Message}");
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء التنقل: {ex.Message}");
            }
        }

        public void OnNavigatedFrom()
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedFrom called");
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync started");

                // Show loading
                LoadingPanel.Visibility = Visibility.Visible;
                EmptyStatePanel.Visibility = Visibility.Collapsed;

                // Load payments
                var payments = await _paymentService.GetAllPaymentsAsync();

                _allPayments.Clear();
                _filteredPayments.Clear();

                foreach (var payment in payments)
                {
                    _allPayments.Add(payment);
                    _filteredPayments.Add(payment);
                }

                // Update statistics
                await UpdateStatisticsAsync();

                // Hide loading and show content
                LoadingPanel.Visibility = Visibility.Collapsed;

                if (_filteredPayments.Count == 0)
                {
                    EmptyStatePanel.Visibility = Visibility.Visible;
                }
                else
                {
                    EmptyStatePanel.Visibility = Visibility.Collapsed;
                }

                // Update last update time
                LastUpdateText.Text = DateTime.Now.ToString("HH:mm:ss");

                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync completed successfully");
                _toastService?.ShowSuccess("تم التحديث", "تم تحميل المدفوعات بنجاح");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: LoadPaymentsAsync error: {ex.Message}");
                LoadingPanel.Visibility = Visibility.Collapsed;
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء تحميل المدفوعات: {ex.Message}");
            }
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var statistics = await _paymentService.GetPaymentStatisticsAsync();

                // Update statistics cards
                TotalPaymentsText.Text = statistics.TotalPayments.ToString("N0");
                TotalAmountText.Text = $"{statistics.TotalAmount:N0}";

                // Update cash vs card statistics
                var cashCount = _allPayments.Count(p => p.Method == PaymentMethod.Cash);
                var cardCount = _allPayments.Count(p => p.Method == PaymentMethod.CreditCard);

                CashPaymentsText.Text = cashCount.ToString("N0");
                CardPaymentsText.Text = cardCount.ToString("N0");

                // Update payment count
                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة";

                System.Console.WriteLine("PaymentsPage: Statistics updated successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: UpdateStatisticsAsync error: {ex.Message}");
            }
        }

        private void FilterPayments()
        {
            try
            {
                var searchText = SearchTextBox.Text?.ToLower() ?? "";

                var filtered = _allPayments.Where(payment =>
                {
                    // Search filter
                    bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                                       payment.ReceiptNumber.ToLower().Contains(searchText) ||
                                       payment.SupplierName.ToLower().Contains(searchText) ||
                                       payment.Details.ToLower().Contains(searchText);

                    return matchesSearch;
                }).ToList();

                _filteredPayments.Clear();
                foreach (var payment in filtered)
                {
                    _filteredPayments.Add(payment);
                }

                // Update UI based on filtered results
                if (_filteredPayments.Count == 0 && _allPayments.Count > 0)
                {
                    EmptyStatePanel.Visibility = Visibility.Visible;
                }
                else if (_filteredPayments.Count > 0)
                {
                    EmptyStatePanel.Visibility = Visibility.Collapsed;
                }

                // Update count
                PaymentCountText.Text = $"{_filteredPayments.Count} دفعة";
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: FilterPayments error: {ex.Message}");
            }
        }

        // Event Handlers
        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addPaymentWindow = App.ServiceProvider.GetService(typeof(Windows.AddPaymentWindow)) as Windows.AddPaymentWindow;
                if (addPaymentWindow != null)
                {
                    addPaymentWindow.Owner = Window.GetWindow(this);
                    addPaymentWindow.ShowDialog();

                    if (addPaymentWindow.PaymentSaved)
                    {
                        // Refresh the payments list
                        LoadPaymentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء فتح نافذة إضافة الدفعة: {ex.Message}");
            }
        }

        private void AddMultiPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var multiPaymentWindow = App.ServiceProvider.GetService(typeof(Windows.MultiPaymentWindow)) as Windows.MultiPaymentWindow;
                if (multiPaymentWindow != null)
                {
                    multiPaymentWindow.Owner = Window.GetWindow(this);
                    multiPaymentWindow.ShowDialog();

                    if (multiPaymentWindow.PaymentSaved)
                    {
                        // Refresh the payments list
                        LoadPaymentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء فتح نافذة الوصل المتعدد: {ex.Message}");
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPaymentsAsync();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterPayments();
        }
    }
}
