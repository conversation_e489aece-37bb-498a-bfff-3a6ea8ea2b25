using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using HR_InvoiceArchiver.Controls;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Windows;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class PaymentsPage : UserControl, INavigationAware
    {
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;
        
        public ObservableCollection<Payment> Payments { get; set; } = new();
        public ObservableCollection<Payment> AllPayments { get; set; } = new();

        public PaymentsPage(
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: Constructor started");
                InitializeComponent();
                System.Console.WriteLine("PaymentsPage: InitializeComponent completed");

                _paymentService = paymentService;
                _toastService = toastService;
                _navigationService = navigationService;

                System.Console.WriteLine("PaymentsPage: Services assigned");

                PaymentsDataGrid.ItemsSource = Payments;
                System.Console.WriteLine("PaymentsPage: DataGrid ItemsSource set");

                Loaded += PaymentsPage_Loaded;
                System.Console.WriteLine("PaymentsPage: Constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Constructor error: {ex.Message}");
                System.Console.WriteLine($"PaymentsPage: Constructor stack trace: {ex.StackTrace}");
                throw;
            }
        }

        private async void PaymentsPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadPaymentsAsync();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل صفحة المدفوعات", ex.Message);
                System.Diagnostics.Debug.WriteLine($"PaymentsPage_Loaded Error: {ex}");
            }
        }

        public void OnNavigatedTo(object parameter)
        {
            // Handle navigation parameters
            if (parameter is string action && action == "add")
            {
                // Show add payment dialog
                AddPaymentButton_Click(this, new RoutedEventArgs());
            }
            else if (parameter is string multiAction && multiAction == "multi")
            {
                // Show multi payment dialog
                AddMultiPaymentButton_Click(this, new RoutedEventArgs());
            }
            else
            {
                // Refresh data when navigating to this page
                Dispatcher.BeginInvoke(new Action(async () => await LoadPaymentsAsync()));
            }
        }

        public void OnNavigatedFrom()
        {
            // Cleanup when leaving this page
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync started");
                LoadingPanel.Visibility = Visibility.Visible;
                PaymentsDataGrid.Visibility = Visibility.Collapsed;
                EmptyStatePanel.Visibility = Visibility.Collapsed;

                var payments = await _paymentService.GetAllPaymentsAsync();
                System.Console.WriteLine($"PaymentsPage: Loaded {payments.Count()} payments from service");

                Dispatcher.Invoke(() =>
                {
                    Payments.Clear();
                    AllPayments.Clear();
                    foreach (var payment in payments.OrderByDescending(p => p.PaymentDate))
                    {
                        Payments.Add(payment);
                        AllPayments.Add(payment);
                    }

                    // Hide loading and update UI
                    LoadingPanel.Visibility = Visibility.Collapsed;
                    UpdateEmptyState();
                    UpdatePaymentCount();
                });

                // Load statistics
                await LoadStatisticsAsync();
                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: LoadPaymentsAsync error: {ex.Message}");
                Dispatcher.Invoke(() =>
                {
                    LoadingPanel.Visibility = Visibility.Collapsed;
                    PaymentsDataGrid.Visibility = Visibility.Visible;
                    _toastService.ShowError("خطأ في تحميل المدفوعات", ex.Message);
                });
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: LoadStatisticsAsync started");
                var statistics = await _paymentService.GetPaymentStatisticsAsync();
                System.Console.WriteLine("PaymentsPage: Statistics loaded from service");

                Dispatcher.Invoke(() =>
                {
                    System.Console.WriteLine("PaymentsPage: Updating statistics UI");
                    // Update statistics cards
                    TotalPaymentsText.Text = statistics.TotalPayments.ToString("N0");
                    TotalAmountText.Text = $"{statistics.TotalAmount:N0} د.ع";

                    // Update cash vs card statistics
                    var cashCount = AllPayments.Count(p => p.Method == PaymentMethod.Cash);
                    var cardCount = AllPayments.Count(p => p.Method == PaymentMethod.CreditCard);
                    CashPaymentsText.Text = cashCount.ToString();
                    CardPaymentsText.Text = cardCount.ToString();

                    System.Console.WriteLine("PaymentsPage: Statistics UI updated successfully");
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    _toastService.ShowError("خطأ في تحميل الإحصائيات", ex.Message);
                });
            }
        }

        private void UpdatePaymentCount()
        {
            PaymentCountText.Text = $"{Payments.Count} دفعة";
        }



        private void UpdateEmptyState()
        {
            EmptyStatePanel.Visibility = Payments.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            PaymentsDataGrid.Visibility = Payments.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
        }

        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowPaymentForm();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في إضافة الدفعة", ex.Message);
            }
        }

        private void AddMultiPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowMultiPaymentForm();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في إضافة الوصل المتعدد", ex.Message);
            }
        }

        private void ShowMultiPaymentForm()
        {
            try
            {
                // Create the multi-payment form control
                var multiPaymentForm = new Controls.MultiPaymentFormControl();

                // Set up event handlers
                multiPaymentForm.FormClosed += async (s, e) =>
                {
                    try
                    {
                        // Remove the form from the main window first
                        RemoveMultiPaymentFormFromMainWindow(multiPaymentForm);

                        // If successful, reload payments
                        if (e.Success)
                        {
                            // Show enhanced success message
                            _toastService.ShowSuccess("تم حفظ الوصل المتعدد بنجاح",
                                "تم إنشاء وصولات دفع منفصلة لكل فاتورة وتحديث الجدول!");

                            // Refresh the payments list immediately with delay for better UX
                            await Task.Delay(800);
                            await LoadPaymentsAsync();

                            // Try to select the most recent payments
                            if (Payments.Count > 0)
                            {
                                var recentPayments = Payments.OrderByDescending(p => p.CreatedDate).Take(3);
                                if (recentPayments.Any())
                                {
                                    PaymentsDataGrid.SelectedItem = recentPayments.First();
                                    PaymentsDataGrid.ScrollIntoView(recentPayments.First());
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _toastService.ShowError("خطأ في إغلاق نموذج الدفع المتعدد", ex.Message);
                    }
                };

                // Add to main window
                AddMultiPaymentFormToMainWindow(multiPaymentForm);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في عرض نموذج الدفع المتعدد", ex.Message);
            }
        }

        private void ShowPaymentForm(Payment? payment = null)
        {
            try
            {
                // Create the payment form control
                var paymentForm = new Controls.PaymentFormControl(payment);

                // Set up event handlers
                paymentForm.PaymentSaved += async (s, e) =>
                {
                    try
                    {
                        // Show success message
                        string message = payment != null ?
                            "تم تحديث وصل الدفع وتحديث الجدول بنجاح!" :
                            "تم إضافة وصل الدفع وتحديث الجدول بنجاح!";

                        _toastService.ShowSuccess("تم بنجاح", message);

                        // Refresh the payments list immediately
                        await Task.Delay(500); // Small delay for better UX
                        await LoadPaymentsAsync();

                        // Scroll to the newly added/updated payment if possible
                        if (Payments.Count > 0)
                        {
                            var latestPayment = Payments.OrderByDescending(p => p.CreatedDate).FirstOrDefault();
                            if (latestPayment != null)
                            {
                                PaymentsDataGrid.SelectedItem = latestPayment;
                                PaymentsDataGrid.ScrollIntoView(latestPayment);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in PaymentSaved handler: {ex.Message}");
                        // Still refresh the list even if there's an error with selection
                        await LoadPaymentsAsync();
                    }
                };

                paymentForm.FormClosed += (s, e) =>
                {
                    RemovePaymentFormFromMainWindow(paymentForm);
                };

                // Add to main window
                AddPaymentFormToMainWindow(paymentForm);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في عرض نموذج الدفع", ex.Message);
            }
        }

        private void AddPaymentFormToMainWindow(Controls.PaymentFormControl paymentForm)
        {
            var mainWindow = Application.Current.MainWindow;
            if (mainWindow == null) return;

            // Try different approaches to find the main container
            Grid? targetGrid = null;

            // Approach 1: DialogHost content
            if (mainWindow.Content is MaterialDesignThemes.Wpf.DialogHost dialogHost)
            {
                targetGrid = dialogHost.Content as Grid;
            }
            // Approach 2: Direct Grid
            else if (mainWindow.Content is Grid directGrid)
            {
                targetGrid = directGrid;
            }
            // Approach 3: Find first Grid in visual tree
            else
            {
                targetGrid = FindVisualChild<Grid>(mainWindow);
            }

            if (targetGrid != null)
            {
                // Create overlay container
                var overlayGrid = new Grid
                {
                    Background = new SolidColorBrush(Color.FromArgb(128, 0, 0, 0)), // Semi-transparent background
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    VerticalAlignment = VerticalAlignment.Stretch
                };

                // Add click handler to close on background click
                overlayGrid.MouseLeftButtonDown += (s, e) =>
                {
                    if (e.Source == overlayGrid)
                    {
                        RemovePaymentFormFromMainWindow(paymentForm);
                    }
                };

                // Set overlay properties for the form
                paymentForm.HorizontalAlignment = HorizontalAlignment.Center;
                paymentForm.VerticalAlignment = VerticalAlignment.Center;

                // Add form to overlay
                overlayGrid.Children.Add(paymentForm);

                // Set high z-index and span all rows/columns
                Grid.SetRowSpan(overlayGrid, Math.Max(1, targetGrid.RowDefinitions.Count));
                Grid.SetColumnSpan(overlayGrid, Math.Max(1, targetGrid.ColumnDefinitions.Count));
                Panel.SetZIndex(overlayGrid, 1000);

                // Store reference to overlay for removal
                paymentForm.Tag = overlayGrid;

                targetGrid.Children.Add(overlayGrid);
            }
        }

        private void RemovePaymentFormFromMainWindow(Controls.PaymentFormControl paymentForm)
        {
            try
            {
                // Get the overlay grid from Tag
                if (paymentForm.Tag is Grid overlayGrid)
                {
                    var parent = overlayGrid.Parent as Grid;
                    parent?.Children.Remove(overlayGrid);
                }
                else
                {
                    // Fallback to old method
                    var parent = paymentForm.Parent as Grid;
                    parent?.Children.Remove(paymentForm);
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في إزالة نموذج الدفع", ex.Message);
            }
        }

        private void AddMultiPaymentFormToMainWindow(MultiPaymentFormControl multiPaymentForm)
        {
            try
            {
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow?.Content == null) return;

                // Try different approaches to find the main container
                Grid? targetGrid = null;

                // Approach 1: DialogHost content
                if (mainWindow.Content is MaterialDesignThemes.Wpf.DialogHost dialogHost)
                {
                    targetGrid = dialogHost.Content as Grid;
                }
                // Approach 2: Direct Grid
                else if (mainWindow.Content is Grid directGrid)
                {
                    targetGrid = directGrid;
                }
                // Approach 3: Find first Grid in visual tree
                else
                {
                    targetGrid = FindVisualChild<Grid>(mainWindow);
                }

                if (targetGrid != null)
                {
                    // Create overlay container
                    var overlayGrid = new Grid
                    {
                        Background = new SolidColorBrush(Color.FromArgb(128, 0, 0, 0)), // Semi-transparent background
                        HorizontalAlignment = HorizontalAlignment.Stretch,
                        VerticalAlignment = VerticalAlignment.Stretch
                    };

                    // Add click handler to close on background click
                    overlayGrid.MouseLeftButtonDown += (s, e) =>
                    {
                        if (e.Source == overlayGrid)
                        {
                            RemoveMultiPaymentFormFromMainWindow(multiPaymentForm);
                        }
                    };

                    // Set overlay properties for the form
                    multiPaymentForm.HorizontalAlignment = HorizontalAlignment.Center;
                    multiPaymentForm.VerticalAlignment = VerticalAlignment.Center;
                    multiPaymentForm.Margin = new Thickness(20);

                    // Add form to overlay
                    overlayGrid.Children.Add(multiPaymentForm);

                    // Set high z-index and span all rows/columns
                    Grid.SetRowSpan(overlayGrid, Math.Max(1, targetGrid.RowDefinitions.Count));
                    Grid.SetColumnSpan(overlayGrid, Math.Max(1, targetGrid.ColumnDefinitions.Count));
                    Panel.SetZIndex(overlayGrid, 1000);

                    // Store reference to overlay for removal
                    multiPaymentForm.Tag = overlayGrid;

                    targetGrid.Children.Add(overlayGrid);
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في عرض نموذج الدفع المتعدد", ex.Message);
            }
        }

        private void RemoveMultiPaymentFormFromMainWindow(MultiPaymentFormControl multiPaymentForm)
        {
            try
            {
                // Get the overlay grid from Tag
                if (multiPaymentForm.Tag is Grid overlayGrid)
                {
                    var parent = overlayGrid.Parent as Grid;
                    parent?.Children.Remove(overlayGrid);
                }
                else
                {
                    // Fallback to old method
                    var parent = multiPaymentForm.Parent as Grid;
                    parent?.Children.Remove(multiPaymentForm);
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في إزالة نموذج الدفع المتعدد", ex.Message);
            }
        }

        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPaymentsAsync();
        }

        private void PaymentsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (PaymentsDataGrid.SelectedItem is Payment selectedPayment)
            {
                try
                {
                    ShowPaymentForm(selectedPayment);
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ في تحديث الدفعة", ex.Message);
                }
            }
        }

        // Statistics Card Animation Handlers
        private void StatCard_MouseEnter(object sender, MouseEventArgs e)
        {
            if (sender is FrameworkElement card)
            {
                var transform = card.RenderTransform as ScaleTransform;
                if (transform != null)
                {
                    var storyboard = new System.Windows.Media.Animation.Storyboard();
                    var scaleXAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        To = 1.05,
                        Duration = TimeSpan.FromMilliseconds(200),
                        EasingFunction = new System.Windows.Media.Animation.BackEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                    };
                    var scaleYAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        To = 1.05,
                        Duration = TimeSpan.FromMilliseconds(200),
                        EasingFunction = new System.Windows.Media.Animation.BackEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                    };

                    System.Windows.Media.Animation.Storyboard.SetTarget(scaleXAnimation, transform);
                    System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleXAnimation, new PropertyPath("ScaleX"));
                    System.Windows.Media.Animation.Storyboard.SetTarget(scaleYAnimation, transform);
                    System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleYAnimation, new PropertyPath("ScaleY"));

                    storyboard.Children.Add(scaleXAnimation);
                    storyboard.Children.Add(scaleYAnimation);
                    storyboard.Begin();
                }
            }
        }

        private void StatCard_MouseLeave(object sender, MouseEventArgs e)
        {
            if (sender is FrameworkElement card)
            {
                var transform = card.RenderTransform as ScaleTransform;
                if (transform != null)
                {
                    var storyboard = new System.Windows.Media.Animation.Storyboard();
                    var scaleXAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        To = 1.0,
                        Duration = TimeSpan.FromMilliseconds(200),
                        EasingFunction = new System.Windows.Media.Animation.BackEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                    };
                    var scaleYAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        To = 1.0,
                        Duration = TimeSpan.FromMilliseconds(200),
                        EasingFunction = new System.Windows.Media.Animation.BackEase { EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut }
                    };

                    System.Windows.Media.Animation.Storyboard.SetTarget(scaleXAnimation, transform);
                    System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleXAnimation, new PropertyPath("ScaleX"));
                    System.Windows.Media.Animation.Storyboard.SetTarget(scaleYAnimation, transform);
                    System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleYAnimation, new PropertyPath("ScaleY"));

                    storyboard.Children.Add(scaleXAnimation);
                    storyboard.Children.Add(scaleYAnimation);
                    storyboard.Begin();
                }
            }
        }

        // Search functionality with debouncing
        private System.Windows.Threading.DispatcherTimer? _searchTimer;

        private async void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Cancel previous search timer
            _searchTimer?.Stop();

            // Create new timer for debouncing (wait 300ms after user stops typing)
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(300)
            };

            _searchTimer.Tick += async (s, args) =>
            {
                _searchTimer.Stop();
                await PerformSearchAsync();
            };

            _searchTimer.Start();
        }

        private async Task PerformSearchAsync()
        {
            var searchText = SearchTextBox.Text?.Trim();

            if (string.IsNullOrEmpty(searchText))
            {
                // Show all payments
                await LoadPaymentsAsync();
            }
            else
            {
                try
                {
                    // Show loading state
                    LoadingPanel.Visibility = Visibility.Visible;
                    PaymentsDataGrid.Visibility = Visibility.Collapsed;
                    EmptyStatePanel.Visibility = Visibility.Collapsed;

                    var searchResults = await _paymentService.SearchPaymentsAsync(searchText);

                    Dispatcher.Invoke(() =>
                    {
                        Payments.Clear();
                        foreach (var payment in searchResults.OrderByDescending(p => p.PaymentDate))
                        {
                            Payments.Add(payment);
                        }

                        // Hide loading and update UI
                        LoadingPanel.Visibility = Visibility.Collapsed;
                        UpdateEmptyState();
                        UpdatePaymentCount();

                        // Show search results count
                        if (Payments.Count > 0)
                        {
                            PaymentCountText.Text = $"{Payments.Count} نتيجة بحث";
                        }
                    });
                }
                catch (Exception ex)
                {
                    LoadingPanel.Visibility = Visibility.Collapsed;
                    PaymentsDataGrid.Visibility = Visibility.Visible;
                    _toastService.ShowError("خطأ في البحث", ex.Message);
                }
            }
        }

        private void ViewPaymentAttachment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    if (string.IsNullOrEmpty(payment.AttachmentPath))
                    {
                        _toastService.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                        return;
                    }

                    var fullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments", payment.AttachmentPath);

                    if (File.Exists(fullPath))
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = fullPath,
                                UseShellExecute = true
                            });
                            _toastService.ShowSuccess("تم فتح المرفق", $"تم فتح مرفق الوصل رقم {payment.ReceiptNumber} بنجاح");
                        }
                        catch (Exception ex)
                        {
                            _toastService.ShowError("خطأ في فتح الملف", $"فشل في فتح المرفق: {ex.Message}");
                        }
                    }
                    else
                    {
                        _toastService.ShowError("ملف غير موجود", "مرفق الوصل غير موجود في المسار المحدد");
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في عرض مرفق الوصل: {ex.Message}");
            }
        }

        private void QuickFilterToday_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var today = DateTime.Today;
                var todayPayments = AllPayments.Where(p => p.PaymentDate.Date == today).ToList();

                Payments.Clear();
                foreach (var payment in todayPayments)
                {
                    Payments.Add(payment);
                }

                PaymentCountText.Text = $"{Payments.Count} دفعة";
                _toastService.ShowInfo("تم التطبيق", $"تم عرض مدفوعات اليوم ({Payments.Count} دفعة)");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterThisMonth_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                var monthPayments = AllPayments.Where(p => p.PaymentDate.Month == currentMonth && p.PaymentDate.Year == currentYear).ToList();

                Payments.Clear();
                foreach (var payment in monthPayments)
                {
                    Payments.Add(payment);
                }

                PaymentCountText.Text = $"{Payments.Count} دفعة";
                _toastService.ShowInfo("تم التطبيق", $"تم عرض مدفوعات هذا الشهر ({Payments.Count} دفعة)");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterCash_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var cashPayments = AllPayments.Where(p => p.Method == PaymentMethod.Cash).ToList();

                Payments.Clear();
                foreach (var payment in cashPayments)
                {
                    Payments.Add(payment);
                }

                PaymentCountText.Text = $"{Payments.Count} دفعة";
                _toastService.ShowInfo("تم التطبيق", $"تم عرض المدفوعات النقدية ({Payments.Count} دفعة)");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterCard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var cardPayments = AllPayments.Where(p => p.Method == PaymentMethod.CreditCard).ToList();

                Payments.Clear();
                foreach (var payment in cardPayments)
                {
                    Payments.Add(payment);
                }

                PaymentCountText.Text = $"{Payments.Count} دفعة";
                _toastService.ShowInfo("تم التطبيق", $"تم عرض مدفوعات البطاقة ({Payments.Count} دفعة)");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Show all payments
                Payments.Clear();
                foreach (var payment in AllPayments)
                {
                    Payments.Add(payment);
                }

                PaymentCountText.Text = $"{Payments.Count} دفعة";
                _toastService.ShowInfo("تم التطبيق", "تم عرض جميع المدفوعات");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }
    }
}
