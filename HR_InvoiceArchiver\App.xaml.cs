using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Pages;
using HR_InvoiceArchiver.Windows;

namespace HR_InvoiceArchiver;

public partial class App : Application
{
    public static ServiceProvider ServiceProvider { get; private set; } = null!;

    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            // Configure services
            var services = new ServiceCollection();
            ConfigureServices(services);
            ServiceProvider = services.BuildServiceProvider();

            // Initialize database and seed data
            InitializeDatabase();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // Logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // Database
        services.AddDbContext<DatabaseContext>();

        // Repositories
        services.AddScoped<IInvoiceRepository, InvoiceRepository>();
        services.AddScoped<ISupplierRepository, SupplierRepository>();
        services.AddScoped<IPaymentRepository, PaymentRepository>();

        // Services
        services.AddSingleton<INavigationService, NavigationService>();
        services.AddScoped<IInvoiceService, InvoiceService>();
        services.AddScoped<ISupplierService, SupplierService>();
        services.AddScoped<IPaymentService, PaymentService>();
        services.AddScoped<IToastService, ToastService>();
        services.AddScoped<IDashboardService, DashboardService>();

        // Pages
        services.AddTransient<DashboardPage>();
        services.AddTransient<InvoicesPage>();
        services.AddTransient<SuppliersPage>();
        services.AddTransient<PaymentsPage>();
        services.AddTransient<TestPaymentsPage>();
        services.AddTransient<SearchPage>();
        services.AddTransient<ReportsPage>();
    }

    private void InitializeDatabase()
    {
        try
        {
            using var scope = ServiceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<DatabaseContext>();

            // Create database if it doesn't exist (without deleting existing data)
            context.Database.EnsureCreated();

            // Only seed sample data if database is empty
            SeedSampleData(context);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}\n\nDetails: {ex.InnerException?.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void SeedSampleData(DatabaseContext context)
    {
        // Check if data already exists
        if (context.Suppliers.Any())
        {
            return;
        }

        // Add sample suppliers
        var suppliers = new[]
        {
            new HR_InvoiceArchiver.Models.Supplier { Name = "شركة الأمل للتجارة", Phone = "07801234567", Address = "بغداد - الكرادة" },
            new HR_InvoiceArchiver.Models.Supplier { Name = "مؤسسة النور التجارية", Phone = "07709876543", Address = "البصرة - المعقل" },
            new HR_InvoiceArchiver.Models.Supplier { Name = "شركة الفجر للمواد", Phone = "07501122334", Address = "أربيل - عنكاوا" }
        };

        context.Suppliers.AddRange(suppliers);
        context.SaveChanges();

        // Add sample invoices
        var invoices = new[]
        {
            new HR_InvoiceArchiver.Models.Invoice
            {
                InvoiceNumber = "INV-001",
                SupplierId = suppliers[0].Id,
                Amount = 1500000,
                InvoiceDate = DateTime.Now.AddDays(-30),
                DueDate = DateTime.Now.AddDays(-15),
                Status = HR_InvoiceArchiver.Models.InvoiceStatus.PartiallyPaid,
                Description = "مواد بناء متنوعة"
            },
            new HR_InvoiceArchiver.Models.Invoice
            {
                InvoiceNumber = "INV-002",
                SupplierId = suppliers[1].Id,
                Amount = 2500000,
                InvoiceDate = DateTime.Now.AddDays(-20),
                DueDate = DateTime.Now.AddDays(10),
                Status = HR_InvoiceArchiver.Models.InvoiceStatus.Unpaid,
                Description = "أجهزة كهربائية"
            },
            new HR_InvoiceArchiver.Models.Invoice
            {
                InvoiceNumber = "INV-003",
                SupplierId = suppliers[2].Id,
                Amount = 800000,
                InvoiceDate = DateTime.Now.AddDays(-10),
                DueDate = DateTime.Now.AddDays(20),
                Status = HR_InvoiceArchiver.Models.InvoiceStatus.Paid,
                Description = "قرطاسية ومكتبية"
            }
        };

        context.Invoices.AddRange(invoices);
        context.SaveChanges();

        // Add sample payments
        var payments = new[]
        {
            new HR_InvoiceArchiver.Models.Payment
            {
                InvoiceId = invoices[0].Id,
                Amount = 1000000,
                PaymentDate = DateTime.Now.AddDays(-25),
                Method = HR_InvoiceArchiver.Models.PaymentMethod.Cash,
                ReceiptNumber = "REC-001",
                Notes = "دفعة جزئية أولى"
            },
            new HR_InvoiceArchiver.Models.Payment
            {
                InvoiceId = invoices[2].Id,
                Amount = 800000,
                PaymentDate = DateTime.Now.AddDays(-5),
                Method = HR_InvoiceArchiver.Models.PaymentMethod.CreditCard,
                ReceiptNumber = "REC-002",
                Notes = "دفعة كاملة"
            }
        };

        context.Payments.AddRange(payments);
        context.SaveChanges();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        ServiceProvider?.Dispose();
        base.OnExit(e);
    }
}
