<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <materialDesign:Card Grid.Row="0" Margin="20" Padding="30" Background="#007BFF">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="CreditCard" Width="40" Height="40" Foreground="White" Margin="0,0,15,0"/>
                <TextBlock Text="إدارة المدفوعات"
                          FontSize="28"
                          FontWeight="Bold"
                          Foreground="White"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Statistics Cards -->
        <Grid Grid.Row="1" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Payments Card -->
            <materialDesign:Card Grid.Column="0" Margin="5" Padding="20" Background="#E3F2FD">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24" Foreground="#1976D2" Margin="0,0,8,0"/>
                        <TextBlock Text="إجمالي المدفوعات" FontSize="16" Foreground="#1976D2" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#0D47A1" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#1976D2" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Total Amount Card -->
            <materialDesign:Card Grid.Column="1" Margin="5" Padding="20" Background="#E8F5E8">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24" Foreground="#388E3C" Margin="0,0,8,0"/>
                        <TextBlock Text="إجمالي المبلغ" FontSize="16" Foreground="#388E3C" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalAmountText" Text="0" FontSize="28" FontWeight="Bold" Foreground="#1B5E20" HorizontalAlignment="Center"/>
                    <TextBlock Text="دينار عراقي" FontSize="14" Foreground="#388E3C" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Cash Payments Card -->
            <materialDesign:Card Grid.Column="2" Margin="5" Padding="20" Background="#FFF3E0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="Cash" Width="24" Height="24" Foreground="#F57C00" Margin="0,0,8,0"/>
                        <TextBlock Text="مدفوعات نقدية" FontSize="16" Foreground="#F57C00" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="CashPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#E65100" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#F57C00" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Card Payments Card -->
            <materialDesign:Card Grid.Column="3" Margin="5" Padding="20" Background="#F3E5F5">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="CreditCardOutline" Width="24" Height="24" Foreground="#7B1FA2" Margin="0,0,8,0"/>
                        <TextBlock Text="مدفوعات بطاقة" FontSize="16" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="CardPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#4A148C" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#7B1FA2" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Main Content -->
        <materialDesign:Card Grid.Row="2" Margin="20" Padding="30">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
                    <Button x:Name="AddPaymentButton"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="#28A745"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="AddPaymentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة مدفوعة"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="AddMultiPaymentButton"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="#007BFF"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="AddMultiPaymentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="وصل متعدد"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RefreshButton"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="#17A2B8"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- Search Section -->
                <TextBox x:Name="SearchTextBox"
                        Grid.Row="1"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        materialDesign:HintAssist.Hint="🔍 البحث في المدفوعات..."
                        FontSize="14"
                        Margin="0,0,0,20"
                        TextChanged="SearchTextBox_TextChanged"/>

                <!-- Payments DataGrid -->
                <DataGrid x:Name="PaymentsDataGrid"
                         Grid.Row="2"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         Background="White"
                         AlternatingRowBackground="#F8F9FA"
                         RowHeight="55"
                         FontSize="14"
                         materialDesign:DataGridAssist.CellPadding="15,8"
                         materialDesign:DataGridAssist.ColumnHeaderPadding="15,12">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="140"/>
                        <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N0}" Width="150"/>
                        <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethodText}" Width="120"/>
                        <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Footer -->
                <Border Grid.Row="3" Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,20,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Information" Width="20" Height="20" Foreground="#6C757D" Margin="0,0,8,0"/>
                            <TextBlock x:Name="PaymentCountText"
                                      Text="0 دفعة"
                                      FontSize="16"
                                      FontWeight="SemiBold"
                                      Foreground="#6C757D"
                                      VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="آخر تحديث: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                            <TextBlock x:Name="LastUpdateText"
                                      Text="الآن"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      Foreground="#007BFF"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Panel -->
        <StackPanel x:Name="LoadingPanel"
                   Grid.Row="2"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   Visibility="Collapsed">
            <materialDesign:Card Padding="40" Background="White">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar IsIndeterminate="True" Width="200" Height="6" Margin="0,0,0,20"/>
                    <TextBlock Text="جاري تحميل المدفوعات..." FontSize="16" Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>

        <!-- Empty State Panel -->
        <StackPanel x:Name="EmptyStatePanel"
                   Grid.Row="2"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   Visibility="Collapsed">
            <materialDesign:Card Padding="60" Background="White">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CreditCardOff" Width="80" Height="80" Foreground="#CCC" Margin="0,0,0,20"/>
                    <TextBlock Text="لا توجد مدفوعات" FontSize="20" FontWeight="SemiBold" Foreground="#999" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="ابدأ بإضافة أول مدفوعة" FontSize="14" Foreground="#CCC" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </Grid>
</UserControl>