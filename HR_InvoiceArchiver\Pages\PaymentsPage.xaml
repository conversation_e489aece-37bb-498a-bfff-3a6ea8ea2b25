<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Enhanced Header Section -->
        <Border Grid.Row="0" Margin="0,0,0,20" CornerRadius="0,0,20,20">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#007BFF" Offset="0"/>
                    <GradientStop Color="#0056B3" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>
            <Grid Margin="40,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CreditCard" Width="50" Height="50" Foreground="White" Margin="0,0,20,0"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="إدارة المدفوعات" FontSize="32" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="نظام إدارة شامل للمدفوعات والإيصالات" FontSize="16" Foreground="#E3F2FD" Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="rgba(255,255,255,0.2)" CornerRadius="10" Padding="15,10" Margin="10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Clock" Width="20" Height="20" Foreground="White" Margin="0,0,8,0"/>
                            <TextBlock x:Name="CurrentTimeText" Text="00:00:00" FontSize="14" Foreground="White" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Border>
                    <Border Background="rgba(255,255,255,0.2)" CornerRadius="10" Padding="15,10" Margin="10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Calendar" Width="20" Height="20" Foreground="White" Margin="0,0,8,0"/>
                            <TextBlock x:Name="CurrentDateText" Text="اليوم" FontSize="14" Foreground="White" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Enhanced Statistics Cards -->
        <Grid Grid.Row="1" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Payments Card -->
            <materialDesign:Card Grid.Column="0" Margin="8" Padding="20" Background="#E3F2FD" Cursor="Hand">
                <materialDesign:Card.Effect>
                    <DropShadowEffect Color="#1976D2" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
                </materialDesign:Card.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                        <Border Background="#1976D2" CornerRadius="25" Width="50" Height="50" Margin="0,0,10,0">
                            <materialDesign:PackIcon Kind="Receipt" Width="28" Height="28" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إجمالي المدفوعات" FontSize="14" Foreground="#1976D2" FontWeight="SemiBold"/>
                            <TextBlock Text="Total Payments" FontSize="10" Foreground="#90CAF9"/>
                        </StackPanel>
                    </StackPanel>

                    <TextBlock Grid.Row="1" x:Name="TotalPaymentsText" Text="0" FontSize="36" FontWeight="Bold" Foreground="#0D47A1" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                    <Border Grid.Row="2" Background="#BBDEFB" CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                        <TextBlock Text="دفعة مسجلة" FontSize="12" Foreground="#1976D2" FontWeight="SemiBold"/>
                    </Border>
                </Grid>
            </materialDesign:Card>

            <!-- Total Amount Card -->
            <materialDesign:Card Grid.Column="1" Margin="8" Padding="20" Background="#E8F5E8" Cursor="Hand">
                <materialDesign:Card.Effect>
                    <DropShadowEffect Color="#388E3C" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
                </materialDesign:Card.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                        <Border Background="#388E3C" CornerRadius="25" Width="50" Height="50" Margin="0,0,10,0">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="28" Height="28" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إجمالي المبلغ" FontSize="14" Foreground="#388E3C" FontWeight="SemiBold"/>
                            <TextBlock Text="Total Amount" FontSize="10" Foreground="#A5D6A7"/>
                        </StackPanel>
                    </StackPanel>

                    <TextBlock Grid.Row="1" x:Name="TotalAmountText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#1B5E20" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                    <Border Grid.Row="2" Background="#C8E6C9" CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                        <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#388E3C" FontWeight="SemiBold"/>
                    </Border>
                </Grid>
            </materialDesign:Card>

            <!-- Cash Payments Card -->
            <materialDesign:Card Grid.Column="2" Margin="5" Padding="20" Background="#FFF3E0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="Cash" Width="24" Height="24" Foreground="#F57C00" Margin="0,0,8,0"/>
                        <TextBlock Text="مدفوعات نقدية" FontSize="16" Foreground="#F57C00" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="CashPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#E65100" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#F57C00" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Card Payments Card -->
            <materialDesign:Card Grid.Column="3" Margin="5" Padding="20" Background="#F3E5F5">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="CreditCardOutline" Width="24" Height="24" Foreground="#7B1FA2" Margin="0,0,8,0"/>
                        <TextBlock Text="مدفوعات بطاقة" FontSize="16" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="CardPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#4A148C" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#7B1FA2" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Main Content -->
        <materialDesign:Card Grid.Row="2" Margin="20" Padding="30">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
                    <Button x:Name="AddPaymentButton" 
                           Style="{StaticResource MaterialDesignRaisedButton}" 
                           Background="#28A745"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="AddPaymentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة مدفوعة"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="AddMultiPaymentButton" 
                           Style="{StaticResource MaterialDesignRaisedButton}" 
                           Background="#007BFF"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="AddMultiPaymentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="وصل متعدد"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RefreshButton" 
                           Style="{StaticResource MaterialDesignRaisedButton}" 
                           Background="#17A2B8"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- Search Section -->
                <TextBox x:Name="SearchTextBox"
                        Grid.Row="1"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        materialDesign:HintAssist.Hint="🔍 البحث في المدفوعات..."
                        FontSize="14"
                        Margin="0,0,0,20"
                        TextChanged="SearchTextBox_TextChanged"/>

                <!-- Payments DataGrid -->
                <DataGrid x:Name="PaymentsDataGrid"
                         Grid.Row="2"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         Background="White"
                         AlternatingRowBackground="#F8F9FA"
                         RowHeight="55"
                         FontSize="14"
                         materialDesign:DataGridAssist.CellPadding="15,8"
                         materialDesign:DataGridAssist.ColumnHeaderPadding="15,12">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="140"/>
                        <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N0}" Width="150"/>
                        <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethodText}" Width="120"/>
                        <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Footer -->
                <Border Grid.Row="3" Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,20,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Information" Width="20" Height="20" Foreground="#6C757D" Margin="0,0,8,0"/>
                            <TextBlock x:Name="PaymentCountText" 
                                      Text="0 دفعة" 
                                      FontSize="16" 
                                      FontWeight="SemiBold" 
                                      Foreground="#6C757D" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="آخر تحديث: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                            <TextBlock x:Name="LastUpdateText" 
                                      Text="الآن" 
                                      FontSize="14" 
                                      FontWeight="SemiBold" 
                                      Foreground="#007BFF" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Panel -->
        <StackPanel x:Name="LoadingPanel" 
                   Grid.Row="2"
                   HorizontalAlignment="Center" 
                   VerticalAlignment="Center" 
                   Visibility="Collapsed">
            <materialDesign:Card Padding="40" Background="White">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar IsIndeterminate="True" Width="200" Height="6" Margin="0,0,0,20"/>
                    <TextBlock Text="جاري تحميل المدفوعات..." FontSize="16" Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>

        <!-- Empty State Panel -->
        <StackPanel x:Name="EmptyStatePanel" 
                   Grid.Row="2"
                   HorizontalAlignment="Center" 
                   VerticalAlignment="Center" 
                   Visibility="Collapsed">
            <materialDesign:Card Padding="60" Background="White">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CreditCardOff" Width="80" Height="80" Foreground="#CCC" Margin="0,0,0,20"/>
                    <TextBlock Text="لا توجد مدفوعات" FontSize="20" FontWeight="SemiBold" Foreground="#999" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="ابدأ بإضافة أول مدفوعة" FontSize="14" Foreground="#CCC" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </Grid>
</UserControl>
