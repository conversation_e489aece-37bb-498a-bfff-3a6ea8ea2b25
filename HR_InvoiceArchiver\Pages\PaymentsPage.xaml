<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007BFF" Padding="20" CornerRadius="0,0,12,12">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="CreditCard" Width="32" Height="32" Foreground="White" Margin="0,0,10,0"/>
                <TextBlock Text="إدارة المدفوعات" 
                          FontSize="24" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <StackPanel Grid.Row="1" Margin="20">
            <!-- Statistics Cards -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Payments Card -->
                <materialDesign:Card Grid.Column="0" Margin="5" Padding="15" Background="#E3F2FD">
                    <StackPanel>
                        <TextBlock Text="إجمالي المدفوعات" FontSize="14" Foreground="#1976D2" FontWeight="SemiBold"/>
                        <TextBlock x:Name="TotalPaymentsText" Text="0" FontSize="24" FontWeight="Bold" Foreground="#0D47A1"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Amount Card -->
                <materialDesign:Card Grid.Column="1" Margin="5" Padding="15" Background="#E8F5E8">
                    <StackPanel>
                        <TextBlock Text="إجمالي المبلغ" FontSize="14" Foreground="#388E3C" FontWeight="SemiBold"/>
                        <TextBlock x:Name="TotalAmountText" Text="0 د.ع" FontSize="20" FontWeight="Bold" Foreground="#1B5E20"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Cash Payments Card -->
                <materialDesign:Card Grid.Column="2" Margin="5" Padding="15" Background="#FFF3E0">
                    <StackPanel>
                        <TextBlock Text="مدفوعات نقدية" FontSize="14" Foreground="#F57C00" FontWeight="SemiBold"/>
                        <TextBlock x:Name="CashPaymentsText" Text="0" FontSize="24" FontWeight="Bold" Foreground="#E65100"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Card Payments Card -->
                <materialDesign:Card Grid.Column="3" Margin="5" Padding="15" Background="#F3E5F5">
                    <StackPanel>
                        <TextBlock Text="مدفوعات بطاقة" FontSize="14" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                        <TextBlock x:Name="CardPaymentsText" Text="0" FontSize="24" FontWeight="Bold" Foreground="#4A148C"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
                <Button x:Name="AddPaymentButton" 
                       Content="إضافة مدفوعة" 
                       Style="{StaticResource MaterialDesignRaisedButton}" 
                       Background="#28A745" 
                       Foreground="White" 
                       Margin="10,0" 
                       Padding="20,10" 
                       FontSize="16"
                       Click="AddPaymentButton_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إضافة مدفوعة"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button x:Name="AddMultiPaymentButton" 
                       Content="وصل متعدد" 
                       Style="{StaticResource MaterialDesignRaisedButton}" 
                       Background="#007BFF" 
                       Foreground="White" 
                       Margin="10,0" 
                       Padding="20,10" 
                       FontSize="16"
                       Click="AddMultiPaymentButton_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="وصل متعدد"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </StackPanel>

            <!-- Search Box -->
            <materialDesign:Card Padding="10" Margin="0,0,0,20" Background="White">
                <TextBox x:Name="SearchTextBox"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        materialDesign:HintAssist.Hint="البحث في المدفوعات..."
                        materialDesign:TextFieldAssist.HasLeadingIcon="True"
                        materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                        FontSize="14"
                        TextChanged="SearchTextBox_TextChanged"/>
            </materialDesign:Card>

            <!-- Payments DataGrid -->
            <materialDesign:Card Padding="0" Background="White">
                <DataGrid x:Name="PaymentsDataGrid"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         MinHeight="300"
                         Background="White"
                         AlternatingRowBackground="#F8F9FA"
                         RowHeight="50"
                         FontSize="14">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="150"/>
                        <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="130"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N0}" Width="160"/>
                        <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethodText}" Width="130"/>
                        <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </materialDesign:Card>

            <!-- Payment Count -->
            <TextBlock x:Name="PaymentCountText" 
                      Text="0 دفعة" 
                      FontSize="16" 
                      FontWeight="SemiBold" 
                      Foreground="#6C757D" 
                      HorizontalAlignment="Center" 
                      Margin="0,10,0,0"/>

            <!-- Empty State Panel -->
            <StackPanel x:Name="EmptyStatePanel" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center" 
                       Visibility="Collapsed"
                       Margin="0,50,0,0">
                <materialDesign:PackIcon Kind="CreditCardOff" Width="64" Height="64" Foreground="#CCC" Margin="0,0,0,20"/>
                <TextBlock Text="لا توجد مدفوعات" FontSize="18" Foreground="#999" HorizontalAlignment="Center"/>
            </StackPanel>

            <!-- Loading Panel -->
            <StackPanel x:Name="LoadingPanel" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center" 
                       Visibility="Collapsed"
                       Margin="0,50,0,0">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,20"/>
                <TextBlock Text="جاري تحميل المدفوعات..." FontSize="16" Foreground="#666" HorizontalAlignment="Center"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
