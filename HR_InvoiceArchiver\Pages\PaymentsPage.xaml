<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">

    <UserControl.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.1" BlurRadius="10" ShadowDepth="3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Button Styles -->
        <Style x:Key="ModernPrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#007BFF" Offset="0"/>
                        <GradientStop Color="#0056B3" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernSecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#28A745" Offset="0"/>
                        <GradientStop Color="#1E7E34" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#28A745" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernWarningButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FFC107" Offset="0"/>
                        <GradientStop Color="#E0A800" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#FFC107" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Section with Gradient -->
            <Border Grid.Row="0" Margin="0,0,0,20" CornerRadius="0,0,20,20">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#007BFF" Offset="0"/>
                        <GradientStop Color="#0056B3" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Border.Effect>
                    <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                </Border.Effect>
                <Grid Margin="40,30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CreditCard" Width="50" Height="50" Foreground="White" Margin="0,0,20,0"/>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إدارة المدفوعات" FontSize="32" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="نظام إدارة شامل للمدفوعات والإيصالات" FontSize="16" Foreground="#E3F2FD" Margin="0,5,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <Border Background="rgba(255,255,255,0.2)" CornerRadius="10" Padding="15,10" Margin="10,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Clock" Width="20" Height="20" Foreground="White" Margin="0,0,8,0"/>
                                <TextBlock x:Name="CurrentTimeText" Text="00:00:00" FontSize="14" Foreground="White" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Border>
                        <Border Background="rgba(255,255,255,0.2)" CornerRadius="10" Padding="15,10" Margin="10,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Calendar" Width="20" Height="20" Foreground="White" Margin="0,0,8,0"/>
                                <TextBlock x:Name="CurrentDateText" Text="اليوم" FontSize="14" Foreground="White" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Enhanced Statistics Cards -->
            <Grid Grid.Row="1" Margin="20,0,20,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Payments Card -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource StatCardStyle}" Background="#E3F2FD" Cursor="Hand">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#1976D2" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                            <Border Background="#1976D2" CornerRadius="25" Width="50" Height="50" Margin="0,0,10,0">
                                <materialDesign:PackIcon Kind="Receipt" Width="28" Height="28" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="إجمالي المدفوعات" FontSize="14" Foreground="#1976D2" FontWeight="SemiBold"/>
                                <TextBlock Text="Total Payments" FontSize="10" Foreground="#90CAF9"/>
                            </StackPanel>
                        </StackPanel>

                        <TextBlock Grid.Row="1" x:Name="TotalPaymentsText" Text="0" FontSize="36" FontWeight="Bold" Foreground="#0D47A1" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                        <Border Grid.Row="2" Background="#BBDEFB" CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                            <TextBlock Text="دفعة مسجلة" FontSize="12" Foreground="#1976D2" FontWeight="SemiBold"/>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Total Amount Card -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource StatCardStyle}" Background="#E8F5E8" Cursor="Hand">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#388E3C" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                            <Border Background="#388E3C" CornerRadius="25" Width="50" Height="50" Margin="0,0,10,0">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="28" Height="28" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="إجمالي المبلغ" FontSize="14" Foreground="#388E3C" FontWeight="SemiBold"/>
                                <TextBlock Text="Total Amount" FontSize="10" Foreground="#A5D6A7"/>
                            </StackPanel>
                        </StackPanel>

                        <TextBlock Grid.Row="1" x:Name="TotalAmountText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#1B5E20" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                        <Border Grid.Row="2" Background="#C8E6C9" CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                            <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#388E3C" FontWeight="SemiBold"/>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Cash Payments Card -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource StatCardStyle}" Background="#FFF3E0" Cursor="Hand">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#F57C00" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                            <Border Background="#F57C00" CornerRadius="25" Width="50" Height="50" Margin="0,0,10,0">
                                <materialDesign:PackIcon Kind="Cash" Width="28" Height="28" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="مدفوعات نقدية" FontSize="14" Foreground="#F57C00" FontWeight="SemiBold"/>
                                <TextBlock Text="Cash Payments" FontSize="10" Foreground="#FFCC80"/>
                            </StackPanel>
                        </StackPanel>

                        <TextBlock Grid.Row="1" x:Name="CashPaymentsText" Text="0" FontSize="36" FontWeight="Bold" Foreground="#E65100" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                        <Border Grid.Row="2" Background="#FFE0B2" CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                            <TextBlock Text="دفعة نقدية" FontSize="12" Foreground="#F57C00" FontWeight="SemiBold"/>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Card Payments Card -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource StatCardStyle}" Background="#F3E5F5" Cursor="Hand">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#7B1FA2" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                            <Border Background="#7B1FA2" CornerRadius="25" Width="50" Height="50" Margin="0,0,10,0">
                                <materialDesign:PackIcon Kind="CreditCardOutline" Width="28" Height="28" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="مدفوعات بطاقة" FontSize="14" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                                <TextBlock Text="Card Payments" FontSize="10" Foreground="#CE93D8"/>
                            </StackPanel>
                        </StackPanel>

                        <TextBlock Grid.Row="1" x:Name="CardPaymentsText" Text="0" FontSize="36" FontWeight="Bold" Foreground="#4A148C" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                        <Border Grid.Row="2" Background="#E1BEE7" CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                            <TextBlock Text="دفعة بطاقة" FontSize="12" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Average Payment Card -->
                <materialDesign:Card Grid.Column="4" Style="{StaticResource StatCardStyle}" Background="#FFF8E1" Cursor="Hand">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#FF8F00" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                            <Border Background="#FF8F00" CornerRadius="25" Width="50" Height="50" Margin="0,0,10,0">
                                <materialDesign:PackIcon Kind="Calculator" Width="28" Height="28" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="متوسط المدفوعة" FontSize="14" Foreground="#FF8F00" FontWeight="SemiBold"/>
                                <TextBlock Text="Average Payment" FontSize="10" Foreground="#FFCC80"/>
                            </StackPanel>
                        </StackPanel>

                        <TextBlock Grid.Row="1" x:Name="AveragePaymentText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#E65100" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                        <Border Grid.Row="2" Background="#FFECB3" CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                            <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#FF8F00" FontWeight="SemiBold"/>
                        </Border>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Enhanced Action Buttons Section -->
            <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCardStyle}" Margin="20,0,20,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Section Title -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="15,10" Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Cog" Width="24" Height="24" Foreground="#6C757D" Margin="0,0,10,0"/>
                            <TextBlock Text="العمليات والإجراءات" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                        </StackPanel>
                    </Border>

                    <!-- Action Buttons Grid -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" x:Name="AddPaymentButton"
                               Style="{StaticResource ModernSecondaryButtonStyle}"
                               Margin="5"
                               Click="AddPaymentButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Plus" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="إضافة مدفوعة" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Add Payment" FontSize="10" Foreground="#E8F5E8" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="1" x:Name="AddMultiPaymentButton"
                               Style="{StaticResource ModernPrimaryButtonStyle}"
                               Margin="5"
                               Click="AddMultiPaymentButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="وصل متعدد" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Multi Receipt" FontSize="10" Foreground="#E3F2FD" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="2" x:Name="RefreshButton"
                               Style="{StaticResource ModernPrimaryButtonStyle}"
                               Background="#17A2B8"
                               Margin="5"
                               Click="RefreshButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="تحديث" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Refresh" FontSize="10" Foreground="#E3F2FD" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="3" x:Name="ExportButton"
                               Style="{StaticResource ModernWarningButtonStyle}"
                               Margin="5"
                               Click="ExportButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="FileExport" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="تصدير" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Export" FontSize="10" Foreground="#FFF8E1" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="4" x:Name="PrintButton"
                               Style="{StaticResource ModernWarningButtonStyle}"
                               Background="#6C757D"
                               Margin="5"
                               Click="PrintButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Printer" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="طباعة" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Print" FontSize="10" Foreground="#F8F9FA" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Advanced Search and Filter Section -->
            <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCardStyle}" Margin="20,0,20,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Search Section Title -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="15,10" Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Magnify" Width="24" Height="24" Foreground="#6C757D" Margin="0,0,10,0"/>
                            <TextBlock Text="البحث والفلترة المتقدمة" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                        </StackPanel>
                    </Border>

                    <!-- Search Controls -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- First Row of Search Controls -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0" x:Name="SearchTextBox"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="🔍 البحث في رقم الإيصال، المورد، أو التفاصيل..."
                                    FontSize="14"
                                    Margin="0,0,10,0"
                                    TextChanged="SearchTextBox_TextChanged"/>

                            <ComboBox Grid.Column="1" x:Name="PaymentMethodFilter"
                                     materialDesign:HintAssist.Hint="طريقة الدفع"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     FontSize="14"
                                     Margin="5,0"
                                     SelectionChanged="PaymentMethodFilter_SelectionChanged">
                                <ComboBoxItem Content="الكل"/>
                                <ComboBoxItem Content="نقدي"/>
                                <ComboBoxItem Content="بطاقة ائتمان"/>
                            </ComboBox>

                            <DatePicker Grid.Column="2" x:Name="DateFromFilter"
                                       materialDesign:HintAssist.Hint="من تاريخ"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       FontSize="14"
                                       Margin="5,0"
                                       SelectedDateChanged="DateFromFilter_SelectedDateChanged"/>

                            <DatePicker Grid.Column="3" x:Name="DateToFilter"
                                       materialDesign:HintAssist.Hint="إلى تاريخ"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       FontSize="14"
                                       Margin="5,0,0,0"
                                       SelectedDateChanged="DateToFilter_SelectedDateChanged"/>
                        </Grid>

                        <!-- Second Row of Search Controls -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0" x:Name="AmountFromFilter"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="المبلغ من"
                                    FontSize="14"
                                    Margin="0,0,10,0"
                                    TextChanged="AmountFromFilter_TextChanged"/>

                            <TextBox Grid.Column="1" x:Name="AmountToFilter"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="المبلغ إلى"
                                    FontSize="14"
                                    Margin="5,0"
                                    TextChanged="AmountToFilter_TextChanged"/>

                            <ComboBox Grid.Column="2" x:Name="SupplierFilter"
                                     materialDesign:HintAssist.Hint="اختر المورد"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     FontSize="14"
                                     Margin="5,0"
                                     SelectionChanged="SupplierFilter_SelectionChanged">
                                <ComboBoxItem Content="جميع الموردين"/>
                            </ComboBox>

                            <StackPanel Grid.Column="3" Orientation="Horizontal" Margin="10,0,0,0">
                                <Button x:Name="ClearFiltersButton"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Content="مسح الفلاتر"
                                       Padding="15,8"
                                       FontSize="14"
                                       Margin="5,0"
                                       Click="ClearFiltersButton_Click"/>

                                <Button x:Name="ApplyFiltersButton"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Background="#007BFF"
                                       Foreground="White"
                                       Content="تطبيق"
                                       Padding="15,8"
                                       FontSize="14"
                                       Margin="5,0,0,0"
                                       Click="ApplyFiltersButton_Click"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Enhanced Data Table Section -->
            <materialDesign:Card Grid.Row="4" Style="{StaticResource ModernCardStyle}" Margin="20,0,20,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Table Header -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="15,10" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Table" Width="24" Height="24" Foreground="#6C757D" Margin="0,0,10,0"/>
                                <TextBlock Text="جدول المدفوعات" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                                <Border Background="#007BFF" CornerRadius="10" Padding="8,4" Margin="15,0,0,0">
                                    <TextBlock x:Name="PaymentCountText" Text="0 دفعة" FontSize="12" Foreground="White" FontWeight="SemiBold"/>
                                </Border>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <TextBlock Text="آخر تحديث: " FontSize="12" Foreground="#6C757D" VerticalAlignment="Center"/>
                                <TextBlock x:Name="LastUpdateText" Text="الآن" FontSize="12" FontWeight="SemiBold" Foreground="#007BFF" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Enhanced DataGrid -->
                    <DataGrid Grid.Row="1" x:Name="PaymentsDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             SelectionMode="Single"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Background="White"
                             AlternatingRowBackground="#F8F9FA"
                             RowHeight="60"
                             FontSize="14"
                             materialDesign:DataGridAssist.CellPadding="15,10"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="15,15"
                             BorderThickness="0"
                             CanUserSortColumns="True">

                        <DataGrid.Resources>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="#007BFF"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Height" Value="50"/>
                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                <Setter Property="VerticalContentAlignment" Value="Center"/>
                            </Style>

                            <Style TargetType="DataGridRow">
                                <Setter Property="Background" Value="White"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#BBDEFB"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.Resources>

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="140">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#007BFF"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#495057"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N0}" Width="150">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#28A745"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="طريقة الدفع" Width="140">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding PaymentMethodText}" Value="نقدي">
                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding PaymentMethodText}" Value="بطاقة ائتمان">
                                                            <Setter Property="Background" Value="#F3E5F5"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding PaymentMethodText}" FontSize="12" FontWeight="SemiBold">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding PaymentMethodText}" Value="نقدي">
                                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding PaymentMethodText}" Value="بطاقة ائتمان">
                                                                <Setter Property="Foreground" Value="#7B1FA2"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                        <Setter Property="Margin" Value="10,0"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   ToolTip="عرض التفاصيل"
                                                   Width="30" Height="30"
                                                   Margin="2">
                                                <materialDesign:PackIcon Kind="Eye" Width="16" Height="16" Foreground="#007BFF"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   ToolTip="تعديل"
                                                   Width="30" Height="30"
                                                   Margin="2">
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16" Foreground="#28A745"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   ToolTip="حذف"
                                                   Width="30" Height="30"
                                                   Margin="2">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Foreground="#DC3545"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Enhanced Footer with Statistics -->
                    <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,20,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Information" Width="20" Height="20" Foreground="#007BFF" Margin="0,0,8,0"/>
                                <TextBlock Text="المجموع المعروض: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                                <TextBlock x:Name="DisplayedAmountText" Text="0 د.ع" FontSize="14" FontWeight="Bold" Foreground="#28A745" VerticalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Filter" Width="20" Height="20" Foreground="#FFC107" Margin="0,0,8,0"/>
                                <TextBlock Text="الفلاتر النشطة: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                                <TextBlock x:Name="ActiveFiltersText" Text="0" FontSize="14" FontWeight="Bold" Foreground="#FFC107" VerticalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                                <materialDesign:PackIcon Kind="Database" Width="20" Height="20" Foreground="#17A2B8" Margin="0,0,8,0"/>
                                <TextBlock Text="حالة البيانات: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                                <TextBlock x:Name="DataStatusText" Text="محدثة" FontSize="14" FontWeight="Bold" Foreground="#17A2B8" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </materialDesign:Card>

            <!-- Enhanced Loading Panel -->
            <Grid x:Name="LoadingPanel"
                  Grid.Row="4"
                  Visibility="Collapsed"
                  Background="rgba(248,249,250,0.9)">
                <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center" Padding="50">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="20" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <StackPanel HorizontalAlignment="Center">
                        <Border Background="#007BFF" CornerRadius="50" Width="100" Height="100" Margin="0,0,0,30">
                            <materialDesign:PackIcon Kind="Loading" Width="50" Height="50" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="جاري تحميل المدفوعات..." FontSize="20" FontWeight="SemiBold" Foreground="#007BFF" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="يرجى الانتظار..." FontSize="14" Foreground="#6C757D" HorizontalAlignment="Center"/>
                        <ProgressBar IsIndeterminate="True" Width="300" Height="8" Margin="0,20,0,0" Foreground="#007BFF"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Enhanced Empty State Panel -->
            <Grid x:Name="EmptyStatePanel"
                  Grid.Row="4"
                  Visibility="Collapsed"
                  Background="rgba(248,249,250,0.9)">
                <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center" Padding="60">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#6C757D" Opacity="0.2" BlurRadius="20" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <StackPanel HorizontalAlignment="Center">
                        <Border Background="#F8F9FA" CornerRadius="50" Width="120" Height="120" Margin="0,0,0,30">
                            <materialDesign:PackIcon Kind="CreditCardOff" Width="60" Height="60" Foreground="#6C757D" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="لا توجد مدفوعات" FontSize="24" FontWeight="Bold" Foreground="#495057" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="لم يتم العثور على أي مدفوعات مطابقة للبحث" FontSize="16" Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,0,0,20"/>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Style="{StaticResource ModernSecondaryButtonStyle}"
                                   Content="إضافة أول مدفوعة"
                                   Margin="10,0"
                                   Click="AddPaymentButton_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="إضافة أول مدفوعة"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource ModernPrimaryButtonStyle}"
                                   Content="مسح الفلاتر"
                                   Margin="10,0"
                                   Click="ClearFiltersButton_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FilterRemove" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="مسح الفلاتر"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>