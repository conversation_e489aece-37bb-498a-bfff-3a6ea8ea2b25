<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">

    <UserControl.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
        </Style>

        <!-- Modern Button Styles -->
        <Style x:Key="ModernPrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#007BFF" Offset="0"/>
                        <GradientStop Color="#0056B3" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernSecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#28A745" Offset="0"/>
                        <GradientStop Color="#1E7E34" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#28A745" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Search Box Style -->
        <Style x:Key="SearchBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="10">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                                <TextBlock x:Name="PlaceholderText"
                                         Text="🔍 البحث في المدفوعات..."
                                         Foreground="#9E9E9E"
                                         VerticalAlignment="Center"
                                         Margin="{TemplateBinding Padding}"
                                         IsHitTestVisible="False"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="Text" Value="{x:Null}">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007BFF"/>
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="Text" Value=""/>
                                    <Condition Property="IsFocused" Value="False"/>
                                </MultiTrigger.Conditions>
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </MultiTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCardStyle}" Background="#007BFF" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="CreditCard" Width="40" Height="40" Foreground="White" Margin="0,0,15,0"/>
                <TextBlock Text="إدارة المدفوعات" 
                          FontSize="28" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Statistics Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Payments Card -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource StatCardStyle}" Background="#E3F2FD">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24" Foreground="#1976D2" Margin="0,0,8,0"/>
                        <TextBlock Text="إجمالي المدفوعات" FontSize="16" Foreground="#1976D2" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#0D47A1" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#1976D2" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Total Amount Card -->
            <materialDesign:Card Grid.Column="1" Style="{StaticResource StatCardStyle}" Background="#E8F5E8">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24" Foreground="#388E3C" Margin="0,0,8,0"/>
                        <TextBlock Text="إجمالي المبلغ" FontSize="16" Foreground="#388E3C" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalAmountText" Text="0" FontSize="28" FontWeight="Bold" Foreground="#1B5E20" HorizontalAlignment="Center"/>
                    <TextBlock Text="دينار عراقي" FontSize="14" Foreground="#388E3C" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Cash Payments Card -->
            <materialDesign:Card Grid.Column="2" Style="{StaticResource StatCardStyle}" Background="#FFF3E0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="Cash" Width="24" Height="24" Foreground="#F57C00" Margin="0,0,8,0"/>
                        <TextBlock Text="مدفوعات نقدية" FontSize="16" Foreground="#F57C00" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="CashPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#E65100" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#F57C00" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Card Payments Card -->
            <materialDesign:Card Grid.Column="3" Style="{StaticResource StatCardStyle}" Background="#F3E5F5">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="CreditCardOutline" Width="24" Height="24" Foreground="#7B1FA2" Margin="0,0,8,0"/>
                        <TextBlock Text="مدفوعات بطاقة" FontSize="16" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="CardPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#4A148C" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#7B1FA2" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Action Buttons -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="AddPaymentButton" 
                       Style="{StaticResource ModernSecondaryButtonStyle}" 
                       Margin="10,0"
                       Click="AddPaymentButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة مدفوعة"/>
                    </StackPanel>
                </Button>

                <Button x:Name="AddMultiPaymentButton" 
                       Style="{StaticResource ModernPrimaryButtonStyle}" 
                       Margin="10,0"
                       Click="AddMultiPaymentButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="وصل متعدد"/>
                    </StackPanel>
                </Button>

                <Button x:Name="RefreshButton" 
                       Style="{StaticResource ModernPrimaryButtonStyle}" 
                       Background="#17A2B8"
                       Margin="10,0"
                       Click="RefreshButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="تحديث"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>

        <!-- Main Content -->
        <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Search and Filter Section -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="SearchTextBox"
                            Grid.Column="0"
                            Style="{StaticResource SearchBoxStyle}"
                            Margin="0,0,10,0"
                            TextChanged="SearchTextBox_TextChanged"/>

                    <ComboBox x:Name="PaymentMethodFilter"
                             Grid.Column="1"
                             materialDesign:HintAssist.Hint="طريقة الدفع"
                             Style="{StaticResource MaterialDesignOutlinedComboBox}"
                             FontSize="14"
                             Margin="5,0"
                             SelectionChanged="PaymentMethodFilter_SelectionChanged">
                        <ComboBoxItem Content="الكل"/>
                        <ComboBoxItem Content="نقدي"/>
                        <ComboBoxItem Content="بطاقة ائتمان"/>
                    </ComboBox>

                    <DatePicker x:Name="DateFilter"
                               Grid.Column="2"
                               materialDesign:HintAssist.Hint="التاريخ"
                               Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                               FontSize="14"
                               Margin="5,0,0,0"
                               SelectedDateChanged="DateFilter_SelectedDateChanged"/>
                </Grid>

                <!-- Payments DataGrid -->
                <DataGrid x:Name="PaymentsDataGrid"
                         Grid.Row="1"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         Background="White"
                         AlternatingRowBackground="#F8F9FA"
                         RowHeight="55"
                         FontSize="14"
                         materialDesign:DataGridAssist.CellPadding="15,8"
                         materialDesign:DataGridAssist.ColumnHeaderPadding="15,12">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="140">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Foreground" Value="#333"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Foreground" Value="#333"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Foreground" Value="#333"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N0}" Width="150">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Foreground" Value="#333"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="Foreground" Value="#28A745"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethodText}" Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Foreground" Value="#333"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                        </DataGridTextColumn>
                        
                        <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Foreground" Value="#333"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Footer with Count and Status -->
                <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,20,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Information" Width="20" Height="20" Foreground="#6C757D" Margin="0,0,8,0"/>
                            <TextBlock x:Name="PaymentCountText" 
                                      Text="0 دفعة" 
                                      FontSize="16" 
                                      FontWeight="SemiBold" 
                                      Foreground="#6C757D" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="آخر تحديث: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                            <TextBlock x:Name="LastUpdateText" 
                                      Text="الآن" 
                                      FontSize="14" 
                                      FontWeight="SemiBold" 
                                      Foreground="#007BFF" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Panel -->
        <StackPanel x:Name="LoadingPanel" 
                   Grid.Row="3"
                   HorizontalAlignment="Center" 
                   VerticalAlignment="Center" 
                   Visibility="Collapsed">
            <materialDesign:Card Padding="40" Background="White">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar IsIndeterminate="True" Width="200" Height="6" Margin="0,0,0,20"/>
                    <TextBlock Text="جاري تحميل المدفوعات..." FontSize="16" Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>

        <!-- Empty State Panel -->
        <StackPanel x:Name="EmptyStatePanel" 
                   Grid.Row="3"
                   HorizontalAlignment="Center" 
                   VerticalAlignment="Center" 
                   Visibility="Collapsed">
            <materialDesign:Card Padding="60" Background="White">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CreditCardOff" Width="80" Height="80" Foreground="#CCC" Margin="0,0,0,20"/>
                    <TextBlock Text="لا توجد مدفوعات" FontSize="20" FontWeight="SemiBold" Foreground="#999" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="ابدأ بإضافة أول مدفوعة" FontSize="14" Foreground="#CCC" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </Grid>
</UserControl>
