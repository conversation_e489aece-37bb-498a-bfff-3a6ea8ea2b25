<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <materialDesign:Card Grid.Row="0" Margin="20" Padding="30" Background="#007BFF">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="CreditCard" Width="40" Height="40" Foreground="White" Margin="0,0,15,0"/>
                <TextBlock Text="إدارة المدفوعات"
                          FontSize="28"
                          FontWeight="Bold"
                          Foreground="White"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Statistics Cards -->
        <Grid Grid.Row="1" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Payments Card -->
            <materialDesign:Card Grid.Column="0" Margin="5" Padding="20" Background="#E3F2FD">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24" Foreground="#1976D2" Margin="0,0,8,0"/>
                        <TextBlock Text="إجمالي المدفوعات" FontSize="16" Foreground="#1976D2" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#0D47A1" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#1976D2" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Total Amount Card -->
            <materialDesign:Card Grid.Column="1" Margin="5" Padding="20" Background="#E8F5E8">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24" Foreground="#388E3C" Margin="0,0,8,0"/>
                        <TextBlock Text="إجمالي المبلغ" FontSize="16" Foreground="#388E3C" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalAmountText" Text="0" FontSize="28" FontWeight="Bold" Foreground="#1B5E20" HorizontalAlignment="Center"/>
                    <TextBlock Text="دينار عراقي" FontSize="14" Foreground="#388E3C" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Cash Payments Card -->
            <materialDesign:Card Grid.Column="2" Margin="5" Padding="20" Background="#FFF3E0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="Cash" Width="24" Height="24" Foreground="#F57C00" Margin="0,0,8,0"/>
                        <TextBlock Text="مدفوعات نقدية" FontSize="16" Foreground="#F57C00" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="CashPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#E65100" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#F57C00" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Card Payments Card -->
            <materialDesign:Card Grid.Column="3" Margin="5" Padding="20" Background="#F3E5F5">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="CreditCardOutline" Width="24" Height="24" Foreground="#7B1FA2" Margin="0,0,8,0"/>
                        <TextBlock Text="مدفوعات بطاقة" FontSize="16" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                    </StackPanel>
                    <TextBlock x:Name="CardPaymentsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#4A148C" HorizontalAlignment="Center"/>
                    <TextBlock Text="دفعة" FontSize="14" Foreground="#7B1FA2" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.1" BlurRadius="10" ShadowDepth="3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Main Content -->
        <materialDesign:Card Grid.Row="2" Margin="20" Padding="30">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
                    <Button x:Name="AddPaymentButton"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="#28A745"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="AddPaymentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة مدفوعة"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="AddMultiPaymentButton"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="#007BFF"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="AddMultiPaymentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="وصل متعدد"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RefreshButton"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="#17A2B8"
                           Foreground="White"
                           Margin="10,0"
                           Padding="20,12"
                           FontSize="16"
                           Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- Search Section -->
                <TextBox x:Name="SearchTextBox"
                        Grid.Row="1"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        materialDesign:HintAssist.Hint="🔍 البحث في المدفوعات..."
                        FontSize="14"
                        Margin="0,0,0,20"
                        TextChanged="SearchTextBox_TextChanged"/>

                <!-- Payments DataGrid -->
                <DataGrid x:Name="PaymentsDataGrid"
                         Grid.Row="2"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         Background="White"
                         AlternatingRowBackground="#F8F9FA"
                         RowHeight="55"
                         FontSize="14"
                         materialDesign:DataGridAssist.CellPadding="15,8"
                         materialDesign:DataGridAssist.ColumnHeaderPadding="15,12">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="140"/>
                        <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N0}" Width="150"/>
                        <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethodText}" Width="120"/>
                        <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Footer -->
                <Border Grid.Row="3" Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,20,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Information" Width="20" Height="20" Foreground="#6C757D" Margin="0,0,8,0"/>
                            <TextBlock x:Name="PaymentCountText"
                                      Text="0 دفعة"
                                      FontSize="16"
                                      FontWeight="SemiBold"
                                      Foreground="#6C757D"
                                      VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="آخر تحديث: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                            <TextBlock x:Name="LastUpdateText"
                                      Text="الآن"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      Foreground="#007BFF"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Panel -->
        <StackPanel x:Name="LoadingPanel"
                   Grid.Row="2"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   Visibility="Collapsed">
            <materialDesign:Card Padding="40" Background="White">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar IsIndeterminate="True" Width="200" Height="6" Margin="0,0,0,20"/>
                    <TextBlock Text="جاري تحميل المدفوعات..." FontSize="16" Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>

        <!-- Empty State Panel -->
        <StackPanel x:Name="EmptyStatePanel"
                   Grid.Row="2"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   Visibility="Collapsed">
            <materialDesign:Card Padding="60" Background="White">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CreditCardOff" Width="80" Height="80" Foreground="#CCC" Margin="0,0,0,20"/>
                    <TextBlock Text="لا توجد مدفوعات" FontSize="20" FontWeight="SemiBold" Foreground="#999" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="ابدأ بإضافة أول مدفوعة" FontSize="14" Foreground="#CCC" HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </Grid>
</UserControl>

            <!-- Enhanced Action Buttons Section -->
            <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCardStyle}" Margin="20,0,20,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Section Title -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="15,10" Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Cog" Width="24" Height="24" Foreground="#6C757D" Margin="0,0,10,0"/>
                            <TextBlock Text="العمليات والإجراءات" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                        </StackPanel>
                    </Border>

                    <!-- Action Buttons Grid -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" x:Name="AddPaymentButton"
                               Style="{StaticResource ModernSecondaryButtonStyle}"
                               Margin="5"
                               Click="AddPaymentButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Plus" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="إضافة مدفوعة" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Add Payment" FontSize="10" Foreground="#E8F5E8" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="1" x:Name="AddMultiPaymentButton"
                               Style="{StaticResource ModernPrimaryButtonStyle}"
                               Margin="5"
                               Click="AddMultiPaymentButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="وصل متعدد" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Multi Receipt" FontSize="10" Foreground="#E3F2FD" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="2" x:Name="RefreshButton"
                               Style="{StaticResource ModernPrimaryButtonStyle}"
                               Background="#17A2B8"
                               Margin="5"
                               Click="RefreshButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="تحديث" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Refresh" FontSize="10" Foreground="#E3F2FD" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="3" x:Name="ExportButton"
                               Style="{StaticResource ModernWarningButtonStyle}"
                               Margin="5"
                               Click="ExportButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="FileExport" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="تصدير" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Export" FontSize="10" Foreground="#FFF8E1" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="4" x:Name="PrintButton"
                               Style="{StaticResource ModernWarningButtonStyle}"
                               Background="#6C757D"
                               Margin="5"
                               Click="PrintButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Printer" Width="24" Height="24" Margin="0,0,0,8"/>
                                <TextBlock Text="طباعة" FontSize="14" HorizontalAlignment="Center"/>
                                <TextBlock Text="Print" FontSize="10" Foreground="#F8F9FA" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Advanced Search and Filter Section -->
            <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCardStyle}" Margin="20,0,20,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Search Section Title -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="15,10" Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Magnify" Width="24" Height="24" Foreground="#6C757D" Margin="0,0,10,0"/>
                            <TextBlock Text="البحث والفلترة المتقدمة" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                        </StackPanel>
                    </Border>

                    <!-- Search Controls -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- First Row of Search Controls -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0" x:Name="SearchTextBox"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="🔍 البحث في رقم الإيصال، المورد، أو التفاصيل..."
                                    FontSize="14"
                                    Margin="0,0,10,0"
                                    TextChanged="SearchTextBox_TextChanged"/>

                            <ComboBox Grid.Column="1" x:Name="PaymentMethodFilter"
                                     materialDesign:HintAssist.Hint="طريقة الدفع"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     FontSize="14"
                                     Margin="5,0"
                                     SelectionChanged="PaymentMethodFilter_SelectionChanged">
                                <ComboBoxItem Content="الكل"/>
                                <ComboBoxItem Content="نقدي"/>
                                <ComboBoxItem Content="بطاقة ائتمان"/>
                            </ComboBox>

                            <DatePicker Grid.Column="2" x:Name="DateFromFilter"
                                       materialDesign:HintAssist.Hint="من تاريخ"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       FontSize="14"
                                       Margin="5,0"
                                       SelectedDateChanged="DateFromFilter_SelectedDateChanged"/>

                            <DatePicker Grid.Column="3" x:Name="DateToFilter"
                                       materialDesign:HintAssist.Hint="إلى تاريخ"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       FontSize="14"
                                       Margin="5,0,0,0"
                                       SelectedDateChanged="DateToFilter_SelectedDateChanged"/>
                        </Grid>

                        <!-- Second Row of Search Controls -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0" x:Name="AmountFromFilter"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="المبلغ من"
                                    FontSize="14"
                                    Margin="0,0,10,0"
                                    TextChanged="AmountFromFilter_TextChanged"/>

                            <TextBox Grid.Column="1" x:Name="AmountToFilter"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="المبلغ إلى"
                                    FontSize="14"
                                    Margin="5,0"
                                    TextChanged="AmountToFilter_TextChanged"/>

                            <ComboBox Grid.Column="2" x:Name="SupplierFilter"
                                     materialDesign:HintAssist.Hint="اختر المورد"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     FontSize="14"
                                     Margin="5,0"
                                     SelectionChanged="SupplierFilter_SelectionChanged">
                                <ComboBoxItem Content="جميع الموردين"/>
                            </ComboBox>

                            <StackPanel Grid.Column="3" Orientation="Horizontal" Margin="10,0,0,0">
                                <Button x:Name="ClearFiltersButton"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Content="مسح الفلاتر"
                                       Padding="15,8"
                                       FontSize="14"
                                       Margin="5,0"
                                       Click="ClearFiltersButton_Click"/>

                                <Button x:Name="ApplyFiltersButton"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Background="#007BFF"
                                       Foreground="White"
                                       Content="تطبيق"
                                       Padding="15,8"
                                       FontSize="14"
                                       Margin="5,0,0,0"
                                       Click="ApplyFiltersButton_Click"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Enhanced Data Table Section -->
            <materialDesign:Card Grid.Row="4" Style="{StaticResource ModernCardStyle}" Margin="20,0,20,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Table Header -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="15,10" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Table" Width="24" Height="24" Foreground="#6C757D" Margin="0,0,10,0"/>
                                <TextBlock Text="جدول المدفوعات" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                                <Border Background="#007BFF" CornerRadius="10" Padding="8,4" Margin="15,0,0,0">
                                    <TextBlock x:Name="PaymentCountText" Text="0 دفعة" FontSize="12" Foreground="White" FontWeight="SemiBold"/>
                                </Border>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <TextBlock Text="آخر تحديث: " FontSize="12" Foreground="#6C757D" VerticalAlignment="Center"/>
                                <TextBlock x:Name="LastUpdateText" Text="الآن" FontSize="12" FontWeight="SemiBold" Foreground="#007BFF" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Enhanced DataGrid -->
                    <DataGrid Grid.Row="1" x:Name="PaymentsDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             SelectionMode="Single"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Background="White"
                             AlternatingRowBackground="#F8F9FA"
                             RowHeight="60"
                             FontSize="14"
                             materialDesign:DataGridAssist.CellPadding="15,10"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="15,15"
                             BorderThickness="0"
                             CanUserSortColumns="True">

                        <DataGrid.Resources>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="#007BFF"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Height" Value="50"/>
                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                <Setter Property="VerticalContentAlignment" Value="Center"/>
                            </Style>

                            <Style TargetType="DataGridRow">
                                <Setter Property="Background" Value="White"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#BBDEFB"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.Resources>

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="140">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#007BFF"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#495057"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N0}" Width="150">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#28A745"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="طريقة الدفع" Width="140">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="15" Padding="10,5" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding PaymentMethodText}" Value="نقدي">
                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding PaymentMethodText}" Value="بطاقة ائتمان">
                                                            <Setter Property="Background" Value="#F3E5F5"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding PaymentMethodText}" FontSize="12" FontWeight="SemiBold">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding PaymentMethodText}" Value="نقدي">
                                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding PaymentMethodText}" Value="بطاقة ائتمان">
                                                                <Setter Property="Foreground" Value="#7B1FA2"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                        <Setter Property="Margin" Value="10,0"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   ToolTip="عرض التفاصيل"
                                                   Width="30" Height="30"
                                                   Margin="2">
                                                <materialDesign:PackIcon Kind="Eye" Width="16" Height="16" Foreground="#007BFF"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   ToolTip="تعديل"
                                                   Width="30" Height="30"
                                                   Margin="2">
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16" Foreground="#28A745"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   ToolTip="حذف"
                                                   Width="30" Height="30"
                                                   Margin="2">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Foreground="#DC3545"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Enhanced Footer with Statistics -->
                    <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,20,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Information" Width="20" Height="20" Foreground="#007BFF" Margin="0,0,8,0"/>
                                <TextBlock Text="المجموع المعروض: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                                <TextBlock x:Name="DisplayedAmountText" Text="0 د.ع" FontSize="14" FontWeight="Bold" Foreground="#28A745" VerticalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Filter" Width="20" Height="20" Foreground="#FFC107" Margin="0,0,8,0"/>
                                <TextBlock Text="الفلاتر النشطة: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                                <TextBlock x:Name="ActiveFiltersText" Text="0" FontSize="14" FontWeight="Bold" Foreground="#FFC107" VerticalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                                <materialDesign:PackIcon Kind="Database" Width="20" Height="20" Foreground="#17A2B8" Margin="0,0,8,0"/>
                                <TextBlock Text="حالة البيانات: " FontSize="14" Foreground="#6C757D" VerticalAlignment="Center"/>
                                <TextBlock x:Name="DataStatusText" Text="محدثة" FontSize="14" FontWeight="Bold" Foreground="#17A2B8" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </materialDesign:Card>

            <!-- Enhanced Loading Panel -->
            <Grid x:Name="LoadingPanel"
                  Grid.Row="4"
                  Visibility="Collapsed"
                  Background="rgba(248,249,250,0.9)">
                <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center" Padding="50">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="20" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <StackPanel HorizontalAlignment="Center">
                        <Border Background="#007BFF" CornerRadius="50" Width="100" Height="100" Margin="0,0,0,30">
                            <materialDesign:PackIcon Kind="Loading" Width="50" Height="50" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="جاري تحميل المدفوعات..." FontSize="20" FontWeight="SemiBold" Foreground="#007BFF" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="يرجى الانتظار..." FontSize="14" Foreground="#6C757D" HorizontalAlignment="Center"/>
                        <ProgressBar IsIndeterminate="True" Width="300" Height="8" Margin="0,20,0,0" Foreground="#007BFF"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Enhanced Empty State Panel -->
            <Grid x:Name="EmptyStatePanel"
                  Grid.Row="4"
                  Visibility="Collapsed"
                  Background="rgba(248,249,250,0.9)">
                <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center" Padding="60">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#6C757D" Opacity="0.2" BlurRadius="20" ShadowDepth="5"/>
                    </materialDesign:Card.Effect>
                    <StackPanel HorizontalAlignment="Center">
                        <Border Background="#F8F9FA" CornerRadius="50" Width="120" Height="120" Margin="0,0,0,30">
                            <materialDesign:PackIcon Kind="CreditCardOff" Width="60" Height="60" Foreground="#6C757D" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="لا توجد مدفوعات" FontSize="24" FontWeight="Bold" Foreground="#495057" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="لم يتم العثور على أي مدفوعات مطابقة للبحث" FontSize="16" Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,0,0,20"/>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Style="{StaticResource ModernSecondaryButtonStyle}"
                                   Margin="10,0"
                                   Click="AddPaymentButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="إضافة أول مدفوعة"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource ModernPrimaryButtonStyle}"
                                   Margin="10,0"
                                   Click="ClearFiltersButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FilterRemove" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="مسح الفلاتر"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>