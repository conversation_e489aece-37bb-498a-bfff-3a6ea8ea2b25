<Window x:Class="HR_InvoiceArchiver.Windows.MultiPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="وصل دفع متعدد"
        Height="800"
        Width="1200"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Background="#F8F9FA">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#007BFF" Offset="0"/>
                        <GradientStop Color="#0056B3" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#28A745" Offset="0"/>
                        <GradientStop Color="#1E7E34" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#DC3545" Offset="0"/>
                        <GradientStop Color="#C82333" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007BFF" CornerRadius="0,0,20,20">
            <Border.Effect>
                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>
            <Grid Margin="30,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Receipt" Width="40" Height="40" Foreground="White" Margin="0,0,15,0"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="وصل دفع متعدد" FontSize="24" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="دفع عدة فواتير في وصل واحد" FontSize="14" Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>
                
                <Button Grid.Column="2" 
                       Style="{StaticResource MaterialDesignIconButton}"
                       Width="40" Height="40"
                       Click="CloseButton_Click">
                    <materialDesign:PackIcon Kind="Close" Width="24" Height="24" Foreground="White"/>
                </Button>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="30,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Invoice Selection -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,15,0" Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Supplier Selection -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="10" Padding="15" Margin="0,0,0,20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                <materialDesign:PackIcon Kind="Account" Width="20" Height="20" Foreground="#007BFF" Margin="0,0,10,0"/>
                                <TextBlock Text="اختيار المورد" FontSize="16" FontWeight="SemiBold" Foreground="#495057"/>
                            </StackPanel>
                            
                            <ComboBox Grid.Row="1" x:Name="SupplierComboBox"
                                     materialDesign:HintAssist.Hint="اختر المورد"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     FontSize="14"
                                     SelectionChanged="SupplierComboBox_SelectionChanged"/>
                        </Grid>
                    </Border>

                    <!-- Search and Filter -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0" x:Name="SearchTextBox"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="🔍 البحث في الفواتير..."
                                FontSize="14"
                                Margin="0,0,10,0"
                                TextChanged="SearchTextBox_TextChanged"/>
                        
                        <Button Grid.Column="1" 
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Content="تحديث"
                               Padding="15,8"
                               Click="RefreshButton_Click"/>
                    </Grid>

                    <!-- Available Invoices -->
                    <DataGrid Grid.Row="2" x:Name="AvailableInvoicesDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             SelectionMode="Single"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Background="White"
                             AlternatingRowBackground="#F8F9FA"
                             RowHeight="50"
                             FontSize="12"
                             materialDesign:DataGridAssist.CellPadding="10,5"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="10,8">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                            <DataGridTextColumn Header="المبلغ الكلي" Binding="{Binding TotalAmount, StringFormat=N0}" Width="100"/>
                            <DataGridTextColumn Header="المدفوع" Binding="{Binding PaidAmount, StringFormat=N0}" Width="100"/>
                            <DataGridTextColumn Header="المتبقي" Binding="{Binding RemainingAmount, StringFormat=N0}" Width="100"/>
                            <DataGridTemplateColumn Header="إضافة" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                               Background="#28A745"
                                               Content="إضافة"
                                               Padding="10,5"
                                               FontSize="10"
                                               Click="AddInvoiceButton_Click"
                                               Tag="{Binding}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Right Panel - Selected Invoices and Payment Details -->
            <materialDesign:Card Grid.Column="1" Margin="15,0,0,0" Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Selected Invoices Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <materialDesign:PackIcon Kind="CheckboxMarkedCircle" Width="20" Height="20" Foreground="#28A745" Margin="0,0,10,0"/>
                        <TextBlock Text="الفواتير المحددة" FontSize="16" FontWeight="SemiBold" Foreground="#495057"/>
                        <Border Background="#28A745" CornerRadius="10" Padding="8,4" Margin="10,0,0,0">
                            <TextBlock x:Name="SelectedCountText" Text="0" FontSize="12" Foreground="White" FontWeight="SemiBold"/>
                        </Border>
                    </StackPanel>

                    <!-- Selected Invoices List -->
                    <ListBox Grid.Row="1" x:Name="SelectedInvoicesListBox" Margin="0,0,0,15">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F8F9FA" CornerRadius="8" Padding="10" Margin="0,2">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding InvoiceNumber}" FontWeight="SemiBold" FontSize="12"/>
                                            <TextBlock Text="{Binding RemainingAmount, StringFormat='{}{0:N0} د.ع'}" FontSize="10" Foreground="#28A745"/>
                                        </StackPanel>
                                        
                                        <Button Grid.Column="1" 
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="24" Height="24"
                                               Click="RemoveInvoiceButton_Click"
                                               Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Close" Width="12" Height="12" Foreground="#DC3545"/>
                                        </Button>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <!-- Payment Summary -->
                    <Border Grid.Row="2" Background="#E3F2FD" CornerRadius="10" Padding="15" Margin="0,0,0,15">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Text="ملخص الدفعة" FontSize="14" FontWeight="SemiBold" Foreground="#007BFF" Margin="0,0,0,10"/>
                            
                            <Grid Grid.Row="1" Margin="0,0,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="إجمالي المبلغ:" FontSize="12"/>
                                <TextBlock Grid.Column="1" x:Name="TotalAmountText" Text="0 د.ع" FontSize="12" FontWeight="SemiBold"/>
                            </Grid>
                            
                            <Grid Grid.Row="2" Margin="0,0,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="الخصم:" FontSize="12"/>
                                <TextBlock Grid.Column="1" x:Name="DiscountAmountText" Text="0 د.ع" FontSize="12" FontWeight="SemiBold"/>
                            </Grid>
                            
                            <Grid Grid.Row="3">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="المبلغ النهائي:" FontSize="12" FontWeight="SemiBold"/>
                                <TextBlock Grid.Column="1" x:Name="FinalAmountText" Text="0 د.ع" FontSize="12" FontWeight="Bold" Foreground="#28A745"/>
                            </Grid>
                        </Grid>
                    </Border>

                    <!-- Payment Details Form -->
                    <StackPanel Grid.Row="3">
                        <TextBox x:Name="ReceiptNumberTextBox"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="رقم الإيصال"
                                FontSize="12"
                                Margin="0,0,0,10"/>
                        
                        <DatePicker x:Name="PaymentDatePicker"
                                   materialDesign:HintAssist.Hint="تاريخ الدفعة"
                                   Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                   FontSize="12"
                                   Margin="0,0,0,10"/>
                        
                        <ComboBox x:Name="PaymentMethodComboBox"
                                 materialDesign:HintAssist.Hint="طريقة الدفع"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 FontSize="12"
                                 Margin="0,0,0,10">
                            <ComboBoxItem Content="نقدي"/>
                            <ComboBoxItem Content="بطاقة ائتمان"/>
                        </ComboBox>
                        
                        <TextBox x:Name="DiscountTextBox"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="نسبة الخصم %"
                                FontSize="12"
                                Margin="0,0,0,10"
                                TextChanged="DiscountTextBox_TextChanged"/>
                        
                        <TextBox x:Name="DetailsTextBox"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="تفاصيل الدفعة"
                                FontSize="12"
                                Height="60"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E9ECEF" BorderThickness="0,1,0,0" Padding="30,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" 
                       Style="{StaticResource SuccessButtonStyle}"
                       Margin="10,0"
                       Click="SaveButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الوصل"/>
                    </StackPanel>
                </Button>
                
                <Button x:Name="ClearButton" 
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#FFC107"
                       Margin="10,0"
                       Click="ClearButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Broom" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="مسح الكل"/>
                    </StackPanel>
                </Button>
                
                <Button x:Name="CancelButton" 
                       Style="{StaticResource CancelButtonStyle}"
                       Margin="10,0"
                       Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
