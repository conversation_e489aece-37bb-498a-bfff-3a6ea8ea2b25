<Window x:Class="HR_InvoiceArchiver.Windows.AddPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة دفعة جديدة"
        Height="700"
        Width="900"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F8F9FA">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#007BFF" Offset="0"/>
                        <GradientStop Color="#0056B3" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#28A745" Offset="0"/>
                        <GradientStop Color="#1E7E34" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#DC3545" Offset="0"/>
                        <GradientStop Color="#C82333" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007BFF" CornerRadius="0,0,20,20">
            <Border.Effect>
                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>
            <Grid Margin="30,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Plus" Width="40" Height="40" Foreground="White" Margin="0,0,15,0"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="إضافة دفعة جديدة" FontSize="24" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="إدخال تفاصيل الدفعة والإيصال" FontSize="14" Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>
                
                <Button Grid.Column="2" 
                       Style="{StaticResource MaterialDesignIconButton}"
                       Width="40" Height="40"
                       Click="CloseButton_Click">
                    <materialDesign:PackIcon Kind="Close" Width="24" Height="24" Foreground="White"/>
                </Button>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="30,20">
            <materialDesign:Card Padding="30">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Invoice Selection Section -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="10" Padding="20" Margin="0,0,0,20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                <materialDesign:PackIcon Kind="FileDocument" Width="24" Height="24" Foreground="#007BFF" Margin="0,0,10,0"/>
                                <TextBlock Text="اختيار الفاتورة" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                            </StackPanel>
                            
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <ComboBox Grid.Column="0" x:Name="InvoiceComboBox"
                                         materialDesign:HintAssist.Hint="اختر الفاتورة المراد دفعها"
                                         Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                         FontSize="14"
                                         Margin="0,0,10,0"
                                         SelectionChanged="InvoiceComboBox_SelectionChanged"/>
                                
                                <Button Grid.Column="1" 
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Content="تحديث القائمة"
                                       Padding="15,8"
                                       Click="RefreshInvoicesButton_Click"/>
                            </Grid>
                        </Grid>
                    </Border>

                    <!-- Invoice Details Display -->
                    <Border Grid.Row="1" x:Name="InvoiceDetailsPanel" Background="#E3F2FD" CornerRadius="10" Padding="20" Margin="0,0,0,20" Visibility="Collapsed">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="رقم الفاتورة:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="InvoiceNumberText" Text="-" FontSize="16" FontWeight="SemiBold" Foreground="#007BFF"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="المبلغ الإجمالي:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="InvoiceTotalText" Text="-" FontSize="16" FontWeight="SemiBold" Foreground="#28A745"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="المبلغ المتبقي:" FontSize="12" Foreground="#6C757D"/>
                                <TextBlock x:Name="InvoiceRemainingText" Text="-" FontSize="16" FontWeight="SemiBold" Foreground="#DC3545"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Payment Details Section -->
                    <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="10" Padding="20" Margin="0,0,0,20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                                <materialDesign:PackIcon Kind="CreditCard" Width="24" Height="24" Foreground="#28A745" Margin="0,0,10,0"/>
                                <TextBlock Text="تفاصيل الدفعة" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                            </StackPanel>
                            
                            <!-- First Row -->
                            <Grid Grid.Row="1" Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox Grid.Column="0" x:Name="ReceiptNumberTextBox"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                        materialDesign:HintAssist.Hint="رقم الإيصال"
                                        FontSize="14"
                                        Margin="0,0,10,0"/>
                                
                                <TextBox Grid.Column="1" x:Name="PaymentAmountTextBox"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                        materialDesign:HintAssist.Hint="مبلغ الدفعة"
                                        FontSize="14"
                                        Margin="5,0"
                                        TextChanged="PaymentAmountTextBox_TextChanged"/>
                                
                                <DatePicker Grid.Column="2" x:Name="PaymentDatePicker"
                                           materialDesign:HintAssist.Hint="تاريخ الدفعة"
                                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                           FontSize="14"
                                           Margin="10,0,0,0"/>
                            </Grid>
                            
                            <!-- Second Row -->
                            <Grid Grid.Row="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <ComboBox Grid.Column="0" x:Name="PaymentMethodComboBox"
                                         materialDesign:HintAssist.Hint="طريقة الدفع"
                                         Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                         FontSize="14"
                                         Margin="0,0,10,0">
                                    <ComboBoxItem Content="نقدي"/>
                                    <ComboBoxItem Content="بطاقة ائتمان"/>
                                </ComboBox>
                                
                                <ComboBox Grid.Column="1" x:Name="PaymentStatusComboBox"
                                         materialDesign:HintAssist.Hint="حالة التسديد"
                                         Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                         FontSize="14"
                                         Margin="5,0"
                                         SelectionChanged="PaymentStatusComboBox_SelectionChanged">
                                    <ComboBoxItem Content="تسديد كامل"/>
                                    <ComboBoxItem Content="تسديد جزئي"/>
                                    <ComboBoxItem Content="تسديد مع خصم"/>
                                </ComboBox>
                                
                                <TextBox Grid.Column="2" x:Name="DiscountAmountTextBox"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                        materialDesign:HintAssist.Hint="مبلغ الخصم"
                                        FontSize="14"
                                        Margin="10,0,0,0"
                                        IsEnabled="False"/>
                            </Grid>
                        </Grid>
                    </Border>

                    <!-- Additional Details Section -->
                    <Border Grid.Row="3" Background="#F8F9FA" CornerRadius="10" Padding="20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                <materialDesign:PackIcon Kind="FileText" Width="24" Height="24" Foreground="#FFC107" Margin="0,0,10,0"/>
                                <TextBlock Text="تفاصيل إضافية" FontSize="18" FontWeight="SemiBold" Foreground="#495057"/>
                            </StackPanel>
                            
                            <TextBox Grid.Row="1" x:Name="DetailsTextBox"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="تفاصيل الدفعة (اختياري)"
                                    FontSize="14"
                                    Height="80"
                                    TextWrapping="Wrap"
                                    AcceptsReturn="True"
                                    VerticalScrollBarVisibility="Auto"
                                    Margin="0,0,0,15"/>
                            
                            <!-- File Attachment -->
                            <Grid Grid.Row="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox Grid.Column="0" x:Name="AttachmentPathTextBox"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                        materialDesign:HintAssist.Hint="مرفق الإيصال (اختياري)"
                                        FontSize="14"
                                        IsReadOnly="True"
                                        Margin="0,0,10,0"/>
                                
                                <Button Grid.Column="1" 
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Content="اختيار ملف"
                                       Padding="15,8"
                                       Click="BrowseFileButton_Click"/>
                            </Grid>
                        </Grid>
                    </Border>
                </Grid>
            </materialDesign:Card>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E9ECEF" BorderThickness="0,1,0,0" Padding="30,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" 
                       Style="{StaticResource SuccessButtonStyle}"
                       Margin="10,0"
                       Click="SaveButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الدفعة"/>
                    </StackPanel>
                </Button>
                
                <Button x:Name="SaveAndNewButton" 
                       Style="{StaticResource ModernButtonStyle}"
                       Margin="10,0"
                       Click="SaveAndNewButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSaveEdit" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإضافة جديد"/>
                    </StackPanel>
                </Button>
                
                <Button x:Name="CancelButton" 
                       Style="{StaticResource CancelButtonStyle}"
                       Margin="10,0"
                       Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel" Width="18" Height="18" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
