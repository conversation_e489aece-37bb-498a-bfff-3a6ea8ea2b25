using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Windows
{
    public partial class MultiPaymentWindow : Window
    {
        private readonly IPaymentService _paymentService;
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        
        private List<Invoice> _allInvoices;
        private List<Invoice> _filteredInvoices;
        private ObservableCollection<Invoice> _selectedInvoices;
        private List<Supplier> _suppliers;
        
        public bool PaymentSaved { get; private set; }

        public MultiPaymentWindow(
            IPaymentService paymentService,
            IInvoiceService invoiceService,
            ISupplierService supplierService,
            IToastService toastService)
        {
            InitializeComponent();
            
            _paymentService = paymentService;
            _invoiceService = invoiceService;
            _supplierService = supplierService;
            _toastService = toastService;
            
            _allInvoices = new List<Invoice>();
            _filteredInvoices = new List<Invoice>();
            _selectedInvoices = new ObservableCollection<Invoice>();
            _suppliers = new List<Supplier>();
            
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Set default values
            PaymentDatePicker.SelectedDate = DateTime.Now;
            PaymentMethodComboBox.SelectedIndex = 0; // نقدي
            
            // Bind selected invoices to ListBox
            SelectedInvoicesListBox.ItemsSource = _selectedInvoices;
            
            // Load data
            LoadSuppliersAndInvoices();
        }

        private async void LoadSuppliersAndInvoices()
        {
            try
            {
                // Load suppliers
                _suppliers = await _supplierService.GetAllSuppliersAsync();
                
                SupplierComboBox.Items.Clear();
                SupplierComboBox.Items.Add(new ComboBoxItem { Content = "جميع الموردين", Tag = null });
                
                foreach (var supplier in _suppliers)
                {
                    SupplierComboBox.Items.Add(new ComboBoxItem { Content = supplier.Name, Tag = supplier });
                }
                
                SupplierComboBox.SelectedIndex = 0;
                
                // Load invoices
                await LoadInvoices();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء تحميل البيانات: {ex.Message}");
            }
        }

        private async Task LoadInvoices()
        {
            try
            {
                // Load unpaid and partially paid invoices
                var allInvoices = await _invoiceService.GetAllInvoicesAsync();
                _allInvoices = allInvoices.Where(i => 
                    i.Status == InvoiceStatus.Unpaid || 
                    i.Status == InvoiceStatus.PartiallyPaid).ToList();

                // Add calculated remaining amount
                foreach (var invoice in _allInvoices)
                {
                    invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                }

                FilterInvoices();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء تحميل الفواتير: {ex.Message}");
            }
        }

        private void FilterInvoices()
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? "";
            var selectedSupplier = (SupplierComboBox.SelectedItem as ComboBoxItem)?.Tag as Supplier;

            _filteredInvoices = _allInvoices.Where(invoice =>
            {
                // Search filter
                bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                                   invoice.InvoiceNumber.ToLower().Contains(searchText) ||
                                   invoice.SupplierName.ToLower().Contains(searchText);

                // Supplier filter
                bool matchesSupplier = selectedSupplier == null || invoice.SupplierId == selectedSupplier.Id;

                // Exclude already selected invoices
                bool notSelected = !_selectedInvoices.Any(s => s.Id == invoice.Id);

                return matchesSearch && matchesSupplier && notSelected;
            }).ToList();

            AvailableInvoicesDataGrid.ItemsSource = _filteredInvoices;
        }

        private void SupplierComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterInvoices();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterInvoices();
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Invoice invoice)
            {
                _selectedInvoices.Add(invoice);
                FilterInvoices(); // Refresh to remove from available list
                UpdateSummary();
                
                _toastService?.ShowSuccess("تمت الإضافة", $"تم إضافة الفاتورة {invoice.InvoiceNumber}");
            }
        }

        private void RemoveInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Invoice invoice)
            {
                _selectedInvoices.Remove(invoice);
                FilterInvoices(); // Refresh to add back to available list
                UpdateSummary();
                
                _toastService?.ShowInfo("تم الحذف", $"تم حذف الفاتورة {invoice.InvoiceNumber}");
            }
        }

        private void DiscountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateSummary();
        }

        private void UpdateSummary()
        {
            var totalAmount = _selectedInvoices.Sum(i => i.RemainingAmount);
            
            decimal.TryParse(DiscountTextBox.Text, out decimal discountPercentage);
            var discountAmount = totalAmount * (discountPercentage / 100);
            var finalAmount = totalAmount - discountAmount;

            TotalAmountText.Text = $"{totalAmount:N0} د.ع";
            DiscountAmountText.Text = $"{discountAmount:N0} د.ع ({discountPercentage:F1}%)";
            FinalAmountText.Text = $"{finalAmount:N0} د.ع";
            SelectedCountText.Text = _selectedInvoices.Count.ToString();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (await SaveMultiPayment())
            {
                PaymentSaved = true;
                Close();
            }
        }

        private async Task<bool> SaveMultiPayment()
        {
            try
            {
                // Validate input
                if (!ValidateInput())
                    return false;

                var receiptNumber = ReceiptNumberTextBox.Text.Trim();
                var paymentDate = PaymentDatePicker.SelectedDate.Value;
                var paymentMethod = PaymentMethodComboBox.SelectedIndex == 0 ? PaymentMethod.Cash : PaymentMethod.CreditCard;
                var details = DetailsTextBox.Text.Trim();
                
                decimal.TryParse(DiscountTextBox.Text, out decimal discountPercentage);

                // Create payments for each selected invoice
                foreach (var invoice in _selectedInvoices)
                {
                    var invoiceAmount = invoice.RemainingAmount;
                    var discountAmount = invoiceAmount * (discountPercentage / 100);
                    var paymentAmount = invoiceAmount - discountAmount;

                    var payment = new Payment
                    {
                        InvoiceId = invoice.Id,
                        ReceiptNumber = receiptNumber,
                        Amount = paymentAmount,
                        PaymentDate = paymentDate,
                        Method = paymentMethod,
                        Details = $"{details} (وصل متعدد)",
                        DiscountAmount = discountAmount
                    };

                    await _paymentService.AddPaymentAsync(payment);
                }

                _toastService?.ShowSuccess("تم الحفظ", $"تم حفظ الوصل المتعدد بنجاح ({_selectedInvoices.Count} فاتورة)");
                return true;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء حفظ الوصل: {ex.Message}");
                return false;
            }
        }

        private bool ValidateInput()
        {
            if (_selectedInvoices.Count == 0)
            {
                _toastService?.ShowWarning("تحذير", "يرجى اختيار فاتورة واحدة على الأقل");
                return false;
            }

            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                _toastService?.ShowWarning("تحذير", "يرجى إدخال رقم الإيصال");
                ReceiptNumberTextBox.Focus();
                return false;
            }

            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                _toastService?.ShowWarning("تحذير", "يرجى اختيار تاريخ الدفعة");
                PaymentDatePicker.Focus();
                return false;
            }

            return true;
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            _selectedInvoices.Clear();
            FilterInvoices();
            UpdateSummary();
            
            ReceiptNumberTextBox.Text = "";
            DiscountTextBox.Text = "";
            DetailsTextBox.Text = "";
            
            _toastService?.ShowInfo("تم المسح", "تم مسح جميع البيانات");
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadInvoices();
            _toastService?.ShowInfo("تم التحديث", "تم تحديث قائمة الفواتير");
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
