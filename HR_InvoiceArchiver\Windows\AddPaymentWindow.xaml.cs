using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Windows
{
    public partial class AddPaymentWindow : Window
    {
        private readonly IPaymentService _paymentService;
        private readonly IInvoiceService _invoiceService;
        private readonly IToastService _toastService;
        
        private List<Invoice> _availableInvoices;
        private Invoice _selectedInvoice;
        
        public bool PaymentSaved { get; private set; }

        public AddPaymentWindow(
            IPaymentService paymentService,
            IInvoiceService invoiceService,
            IToastService toastService)
        {
            InitializeComponent();
            
            _paymentService = paymentService;
            _invoiceService = invoiceService;
            _toastService = toastService;
            
            _availableInvoices = new List<Invoice>();
            
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Set default values
            PaymentDatePicker.SelectedDate = DateTime.Now;
            PaymentMethodComboBox.SelectedIndex = 0; // نقدي
            PaymentStatusComboBox.SelectedIndex = 0; // تسديد كامل
            
            // Load invoices
            LoadAvailableInvoices();
        }

        private async void LoadAvailableInvoices()
        {
            try
            {
                // Load unpaid and partially paid invoices
                var allInvoices = await _invoiceService.GetAllInvoicesAsync();
                _availableInvoices = allInvoices.Where(i => 
                    i.Status == InvoiceStatus.Unpaid || 
                    i.Status == InvoiceStatus.PartiallyPaid).ToList();

                InvoiceComboBox.Items.Clear();
                InvoiceComboBox.Items.Add(new ComboBoxItem { Content = "اختر الفاتورة...", Tag = null });

                foreach (var invoice in _availableInvoices)
                {
                    var displayText = $"فاتورة #{invoice.InvoiceNumber} - {invoice.SupplierName} - {invoice.TotalAmount:N0} د.ع";
                    InvoiceComboBox.Items.Add(new ComboBoxItem { Content = displayText, Tag = invoice });
                }

                InvoiceComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء تحميل الفواتير: {ex.Message}");
            }
        }

        private void InvoiceComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (InvoiceComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag is Invoice invoice)
            {
                _selectedInvoice = invoice;
                ShowInvoiceDetails(invoice);
                
                // Auto-fill payment amount with remaining amount
                var remainingAmount = invoice.TotalAmount - invoice.PaidAmount;
                PaymentAmountTextBox.Text = remainingAmount.ToString("F0");
            }
            else
            {
                _selectedInvoice = null;
                HideInvoiceDetails();
                PaymentAmountTextBox.Text = "";
            }
        }

        private void ShowInvoiceDetails(Invoice invoice)
        {
            InvoiceDetailsPanel.Visibility = Visibility.Visible;
            
            InvoiceNumberText.Text = invoice.InvoiceNumber;
            InvoiceTotalText.Text = $"{invoice.TotalAmount:N0} د.ع";
            
            var remainingAmount = invoice.TotalAmount - invoice.PaidAmount;
            InvoiceRemainingText.Text = $"{remainingAmount:N0} د.ع";
        }

        private void HideInvoiceDetails()
        {
            InvoiceDetailsPanel.Visibility = Visibility.Collapsed;
        }

        private void PaymentAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_selectedInvoice != null && decimal.TryParse(PaymentAmountTextBox.Text, out decimal paymentAmount))
            {
                var remainingAmount = _selectedInvoice.TotalAmount - _selectedInvoice.PaidAmount;
                
                // Auto-select payment status based on amount
                if (paymentAmount >= remainingAmount)
                {
                    PaymentStatusComboBox.SelectedIndex = 0; // تسديد كامل
                }
                else if (paymentAmount > 0)
                {
                    PaymentStatusComboBox.SelectedIndex = 1; // تسديد جزئي
                }
            }
        }

        private void PaymentStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Enable/disable discount field based on payment status
            if (PaymentStatusComboBox.SelectedIndex == 2) // تسديد مع خصم
            {
                DiscountAmountTextBox.IsEnabled = true;
                
                if (_selectedInvoice != null && decimal.TryParse(PaymentAmountTextBox.Text, out decimal paymentAmount))
                {
                    var remainingAmount = _selectedInvoice.TotalAmount - _selectedInvoice.PaidAmount;
                    var discountAmount = remainingAmount - paymentAmount;
                    DiscountAmountTextBox.Text = discountAmount > 0 ? discountAmount.ToString("F0") : "0";
                }
            }
            else
            {
                DiscountAmountTextBox.IsEnabled = false;
                DiscountAmountTextBox.Text = "";
            }
        }

        private void BrowseFileButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار مرفق الإيصال",
                Filter = "جميع الملفات (*.*)|*.*|صور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|PDF (*.pdf)|*.pdf",
                FilterIndex = 2
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPathTextBox.Text = openFileDialog.FileName;
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (await SavePayment())
            {
                PaymentSaved = true;
                Close();
            }
        }

        private async void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
        {
            if (await SavePayment())
            {
                PaymentSaved = true;
                ClearForm();
                _toastService?.ShowSuccess("تم الحفظ", "تم حفظ الدفعة بنجاح. يمكنك إضافة دفعة جديدة.");
            }
        }

        private async Task<bool> SavePayment()
        {
            try
            {
                // Validate input
                if (!ValidateInput())
                    return false;

                // Create payment object
                var payment = new Payment
                {
                    InvoiceId = _selectedInvoice.Id,
                    ReceiptNumber = ReceiptNumberTextBox.Text.Trim(),
                    Amount = decimal.Parse(PaymentAmountTextBox.Text),
                    PaymentDate = PaymentDatePicker.SelectedDate.Value,
                    Method = PaymentMethodComboBox.SelectedIndex == 0 ? PaymentMethod.Cash : PaymentMethod.CreditCard,
                    Details = DetailsTextBox.Text.Trim(),
                    AttachmentPath = AttachmentPathTextBox.Text.Trim()
                };

                // Handle discount if applicable
                if (PaymentStatusComboBox.SelectedIndex == 2 && decimal.TryParse(DiscountAmountTextBox.Text, out decimal discount))
                {
                    payment.DiscountAmount = discount;
                }

                // Save payment
                await _paymentService.AddPaymentAsync(payment);

                _toastService?.ShowSuccess("تم الحفظ", "تم حفظ الدفعة بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء حفظ الدفعة: {ex.Message}");
                return false;
            }
        }

        private bool ValidateInput()
        {
            if (_selectedInvoice == null)
            {
                _toastService?.ShowWarning("تحذير", "يرجى اختيار الفاتورة");
                InvoiceComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                _toastService?.ShowWarning("تحذير", "يرجى إدخال رقم الإيصال");
                ReceiptNumberTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(PaymentAmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                _toastService?.ShowWarning("تحذير", "يرجى إدخال مبلغ صحيح");
                PaymentAmountTextBox.Focus();
                return false;
            }

            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                _toastService?.ShowWarning("تحذير", "يرجى اختيار تاريخ الدفعة");
                PaymentDatePicker.Focus();
                return false;
            }

            var remainingAmount = _selectedInvoice.TotalAmount - _selectedInvoice.PaidAmount;
            if (amount > remainingAmount && PaymentStatusComboBox.SelectedIndex != 2)
            {
                _toastService?.ShowWarning("تحذير", $"مبلغ الدفعة أكبر من المبلغ المتبقي ({remainingAmount:N0} د.ع)");
                PaymentAmountTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            InvoiceComboBox.SelectedIndex = 0;
            ReceiptNumberTextBox.Text = "";
            PaymentAmountTextBox.Text = "";
            PaymentDatePicker.SelectedDate = DateTime.Now;
            PaymentMethodComboBox.SelectedIndex = 0;
            PaymentStatusComboBox.SelectedIndex = 0;
            DetailsTextBox.Text = "";
            AttachmentPathTextBox.Text = "";
            DiscountAmountTextBox.Text = "";
            
            _selectedInvoice = null;
            HideInvoiceDetails();
        }

        private void RefreshInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            LoadAvailableInvoices();
            _toastService?.ShowInfo("تم التحديث", "تم تحديث قائمة الفواتير");
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
