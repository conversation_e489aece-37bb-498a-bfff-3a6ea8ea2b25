<UserControl x:Class="HR_InvoiceArchiver.Pages.TestPaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007BFF" Padding="20" CornerRadius="0,0,12,12">
            <TextBlock Text="اختبار واجهة المدفوعات" 
                      FontSize="24" 
                      FontWeight="Bold" 
                      Foreground="White" 
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <StackPanel Grid.Row="1" Margin="20" HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="تم تحميل واجهة المدفوعات بنجاح!" 
                      FontSize="18" 
                      FontWeight="SemiBold" 
                      Foreground="#28A745" 
                      HorizontalAlignment="Center" 
                      Margin="0,0,0,20"/>
            
            <Button Content="اختبار الزر" 
                   Style="{StaticResource MaterialDesignRaisedButton}" 
                   Background="#007BFF" 
                   Foreground="White" 
                   Padding="20,10" 
                   FontSize="16" 
                   Click="TestButton_Click"/>
        </StackPanel>
    </Grid>
</UserControl>
