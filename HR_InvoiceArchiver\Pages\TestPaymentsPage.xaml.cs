using System;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class TestPaymentsPage : UserControl, INavigationAware
    {
        private readonly IToastService _toastService;

        public TestPaymentsPage(IToastService toastService)
        {
            try
            {
                System.Console.WriteLine("TestPaymentsPage: Constructor started");
                InitializeComponent();
                _toastService = toastService;
                System.Console.WriteLine("TestPaymentsPage: Constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"TestPaymentsPage: Constructor error: {ex.Message}");
                System.Console.WriteLine($"TestPaymentsPage: Constructor stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public void OnNavigatedTo(object parameter)
        {
            System.Console.WriteLine("TestPaymentsPage: OnNavigatedTo called");
            _toastService?.ShowSuccess("نجح", "تم تحميل واجهة المدفوعات التجريبية بنجاح!");
        }

        public void OnNavigatedFrom()
        {
            System.Console.WriteLine("TestPaymentsPage: OnNavigatedFrom called");
        }

        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("اختبار", "تم النقر على الزر بنجاح!");
        }
    }
}
